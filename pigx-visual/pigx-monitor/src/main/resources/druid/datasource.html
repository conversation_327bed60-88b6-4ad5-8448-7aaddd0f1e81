<!doctype html>
<html>
	<head>
		<title>Druid DataSourceStat</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
		<link href='css/bootstrap.min.css' rel="stylesheet" />
		<link href="css/style.css" type="text/css" rel="stylesheet"/>
		<script src="js/doT.js" type="text/javascript" charset="utf8"></script>
    	<script type="text/javascript" src="js/jquery.min.js"></script>
    	<script type="text/javascript" src="js/bootstrap.min.js"></script>
		<script src="js/lang.js" type="text/javascript" charset="utf8"></script>
		<script src="js/common.js" type="text/javascript" charset="utf8"></script>
	</head>
	<body>
		
    	<div class="container-fluid">
      		<div class="row-fluid">
        		<div class="span12">
          				<h3>
          					DataSourceStat List
          					<a href="datasource.json" target="_blank">View JSON API</a>
          				</h3>
          				<div class="alert alert-error clearfix" style="margin-bottom: 5px;width: 195px; padding: 2px 15px 2px 10px;">(*) property for user to setup</div>
        		</div>
      		</div> 
    	</div>
    	<script id="datasource-tmpl" type="text/template">
			<ul class="nav nav-tabs" id="datasourceTab">
				{{~ it.Content :datasourceNow:i  }}
					<li class="{{=i==0?'active':''}}" id="datasourceTab-li-{{=i}}">
						<a href="datasource.html#dstab{{=datasourceNow.Identity}}">{{=datasourceNow.Name}}</a>
					</li>
				{{~ }}
			</ul>
			<div class="tab-content">
				{{~ it.Content :datasourceNow:i  }}
				<div class="tab-pane {{=i==0?'active':''}}" id="dstab{{=datasourceNow.Identity}}">
					<h4>
						Basic Info For {{=datasourceNow.Name}}<a href="datasource-{{=datasourceNow.Identity}}.json" target="_blank"  >View JSON API</a>
					</h4>
					<table class="table table-bordered" style="background-color: #fff">
						<tbody>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="UserName">UserName</span></td>
								<td>{{=datasourceNow.UserName}}</td>
								<td  class="lang"  langKey="UserNameDesc">Specify the username used when creating a new connection.</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="URL">URL</span></td>
								<td>{{=datasourceNow.URL}}</td>
								<td  class="lang"  langKey="URLDesc">The JDBC driver connection URL</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="DbType">DbType</span></td>
								<td>{{=datasourceNow.DbType}}</td>
								<td  class="lang"  langKey="DbTypeDesc">database type</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="DriverClassName">DriverClassName</span></td>
								<td>{{=datasourceNow.DriverClassName}}</td>
								<td  class="lang"  langKey="DriverClassNameDesc">The fully qualifed name of the JDBC driver class</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="FilterClassNames">FilterClassNames</span></td>
								<td>{{=datasourceNow.FilterClassNames}}</td>
								<td  class="lang"  langKey="FilterClassNamesDesc">All the fully qualifed name of the filter classes</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="TestOnBorrow">TestOnBorrow</span></td>
								<td>{{=datasourceNow.TestOnBorrow}}</td>
								<td  class="lang"  langKey="TestOnBorrowDesc">	Test or not when borrow a connection</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="TestWhileIdle">TestWhileIdle</span></td>
								<td>{{=datasourceNow.TestWhileIdle}}</td>
								<td  class="lang"  langKey="TestWhileIdleDesc">Test or not when a connection is idle for a while</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="TestOnReturn">TestOnReturn</span></td>
								<td>{{=datasourceNow.TestOnReturn}}</td>
								<td  class="lang"  langKey="TestOnReturnDesc">Test or not when return a connection</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="InitialSize">InitialSize</span></td>
								<td>{{=datasourceNow.InitialSize}}</td>
								<td  class="lang"  langKey="InitialSizeDesc">The size of datasource connections to create when initial a datasource</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="MinIdle">MinIdle</span></td>
								<td>{{=datasourceNow.MinIdle}}</td>
								<td  class="lang"  langKey="MinIdleDesc">The minimum number of connections a pool should hold. </td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="MaxActive">MaxActive</span></td>
								<td>{{=datasourceNow.MaxActive}}</td>
								<td  class="lang"  langKey="MaxActiveDesc">The maximum number of connections for a pool</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="QueryTimeout">QueryTimeout</span></td>
								<td>{{=datasourceNow.QueryTimeout}}</td>
								<td  class="lang"  langKey="QueryTimeoutDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="TransactionQueryTimeout">TransactionQueryTimeout</span></td>
								<td>{{=datasourceNow.TransactionQueryTimeout}}</td>
								<td  class="lang"  langKey="TransactionQueryTimeoutDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="LoginTimeout">LoginTimeout</span></td>
								<td>{{=datasourceNow.LoginTimeout}}</td>
								<td  class="lang"  langKey="LoginTimeoutDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="ValidConnectionCheckerClassName">ValidConnectionCheckerClassName</span></td>
								<td>{{=datasourceNow.ValidConnectionCheckerClassName}}</td>
								<td  class="lang"  langKey="ValidConnectionCheckerClassNameDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="ExceptionSorterClassName">ExceptionSorterClassName</span></td>
								<td>{{=datasourceNow.ExceptionSorterClassName}}</td>
								<td  class="lang"  langKey="ExceptionSorterClassNameDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="DefaultAutoCommit">DefaultAutoCommit</span></td>
								<td>{{=datasourceNow.DefaultAutoCommit}}</td>
								<td  class="lang"  langKey="DefaultAutoCommitDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="DefaultReadOnly">DefaultReadOnly</span></td>
								<td>{{=datasourceNow.DefaultReadOnly}}</td>
								<td  class="lang"  langKey="DefaultReadOnlyDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="DefaultTransactionIsolation">DefaultTransactionIsolation</span></td>
								<td>{{=datasourceNow.DefaultTransactionIsolation}}</td>
								<td  class="lang"  langKey="DefaultTransactionIsolationDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="MinEvictableIdleTimeMillis">MinEvictableIdleTimeMillis</span></td>
								<td>{{=datasourceNow.MinEvictableIdleTimeMillis}}</td>
								<td  class="lang"  langKey="MinEvictableIdleTimeMillis"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="MaxEvictableIdleTimeMillis">MaxEvictableIdleTimeMillis</span></td>
								<td>{{=datasourceNow.MaxEvictableIdleTimeMillis}}</td>
								<td  class="lang"  langKey="MaxEvictableIdleTimeMillis"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="KeepAlive">KeepAlive</span></td>
								<td>{{=datasourceNow.KeepAlive}}</td>
								<td  class="lang"  langKey="KeepAlive"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="FailFast">FailFast</span></td>
								<td>{{=datasourceNow.FailFast}}</td>
								<td  class="lang"  langKey="FailFast"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="PoolPreparedStatements">PoolPreparedStatements</span></td>
								<td>{{=datasourceNow.PoolPreparedStatements}}</td>
								<td  class="lang"  langKey="PoolPreparedStatements"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="MaxPoolPreparedStatementPerConnectionSize">MaxPoolPreparedStatementPerConnectionSize</span></td>
								<td>{{=datasourceNow.MaxPoolPreparedStatementPerConnectionSize}}</td>
								<td  class="lang"  langKey="MaxPoolPreparedStatementPerConnectionSize"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="MaxWait">MaxWait</span></td>
								<td>{{=datasourceNow.MaxWait}}</td>
								<td  class="lang"  langKey="MaxWait"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="MaxWaitThreadCount">MaxWaitThreadCount</span></td>
								<td>{{=datasourceNow.MaxWaitThreadCount}}</td>
								<td  class="lang"  langKey="MaxWaitThreadCount"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="LogDifferentThread">LogDifferentThread</span></td>
								<td>{{=datasourceNow.LogDifferentThread}}</td>
								<td  class="lang"  langKey="LogDifferentThread"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="UseUnfairLock">UseUnfairLock</span></td>
								<td>{{=datasourceNow.UseUnfairLock}}</td>
								<td  class="lang"  langKey="UseUnfairLock"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="InitGlobalVariants">InitGlobalVariants</span></td>
								<td>{{=datasourceNow.InitGlobalVariants}}</td>
								<td  class="lang"  langKey="InitGlobalVariants"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" > *  <span class="lang"  langKey="InitVariants">InitVariants</span></td>
								<td>{{=datasourceNow.InitVariants}}</td>
								<td  class="lang"  langKey="InitVariants"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="NotEmptyWaitCount">NotEmptyWaitCount</span></td>
								<td>{{=datasourceNow.NotEmptyWaitCount}}</td>
								<td  class="lang"  langKey="NotEmptyWaitCountDesc">Total times for wait to get a connection</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="NotEmptyWaitMillis">NotEmptyWaitMillis</span></td>
								<td>{{=datasourceNow.NotEmptyWaitMillis}}</td>
								<td  class="lang"  langKey="NotEmptyWaitMillisDesc">Total millins for wait to get a connection</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="WaitThreadCount">WaitThreadCount</span></td>
								<td>{{=datasourceNow.WaitThreadCount}}</td>
								<td  class="lang"  langKey="WaitThreadCountDesc">The current waiting thread count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="StartTransactionCount">StartTransactionCount</span></td>
								<td>{{=datasourceNow.StartTransactionCount}}</td>
								<td  class="lang"  langKey="StartTransactionCountDesc">The count of start transaction</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="TransactionHistogram">TransactionHistogram</span></td>
								<td>{{=datasourceNow.TransactionHistogram}}</td>
								<td  class="lang"  langKey="TransactionHistogramDesc">The histogram values of transaction time, [0-1 ms, 1-10 ms, 10-100 ms, 100-1 s, 1-10 s, 10-100 s, >100 s]</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PoolingCount">PoolingCount</span></td>
								<td>{{=datasourceNow.PoolingCount}}</td>
								<td  class="lang"  langKey="PoolingCountDesc">The current usefull connection count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PoolingPeak">PoolingPeak</span></td>
								<td>{{=datasourceNow.PoolingPeak}}</td>
								<td  class="lang"  langKey="PoolingPeakDesc">The usefull connection peak count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PoolingPeakTime">PoolingPeakTime</span></td>
								<td>{{=datasourceNow.PoolingPeakTime}}</td>
								<td  class="lang"  langKey="PoolingPeakTimeDesc">The usefull connection peak time</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ActiveCount">ActiveCount</span></td>
								<td>{{=datasourceNow.ActiveCount}}</td>
								<td  class="lang"  langKey="ActiveCountDesc">The current active connection count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ActivePeak">ActivePeak</span></td>
								<td>{{=datasourceNow.ActivePeak}}</td>
								<td  class="lang"  langKey="ActivePeakDesc">The current active connection peak count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ActivePeakTime">ActivePeakTime</span></td>
								<td>{{=datasourceNow.ActivePeakTime}}</td>
								<td  class="lang"  langKey="ActivePeakTimeDesc">The active connection peak time</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="LogicConnectCount">LogicConnectCount</span></td>
								<td>{{=datasourceNow.LogicConnectCount}}</td>
								<td  class="lang"  langKey="LogicConnectCountDesc">Total connect times from datasource</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="LogicCloseCount">LogicCloseCount</span></td>
								<td>{{=datasourceNow.LogicCloseCount}}</td>
								<td  class="lang"  langKey="LogicCloseCountDesc">Total close connect times from datasource</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="LogicConnectErrorCount">LogicConnectErrorCount</span></td>
								<td>{{=datasourceNow.LogicConnectErrorCount}}</td>
								<td  class="lang"  langKey="LogicConnectErrorCountDesc">Total connect error times</td>
							</tr>
                            <tr>
                                <td valign="top" class="td_lable" >   <span class="lang"  langKey="DiscardCount">DiscardCount</span></td>
                                <td>{{=datasourceNow.DiscardCount}}</td>
                                <td  class="lang"  langKey="DiscardCountDesc">Discard connection count with validate fail </td>
                            </tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="RecycleErrorCount">RecycleErrorCount</span></td>
								<td>{{=datasourceNow.RecycleErrorCount}}</td>
								<td  class="lang"  langKey="RecycleErrorCount">Total connect error times</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PhysicalConnectCount">PhysicalConnectCount</span></td>
								<td>{{=datasourceNow.PhysicalConnectCount}}</td>
								<td  class="lang"  langKey="PhysicalConnectCountDesc">Create physical connnection count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PhysicalCloseCount">PhysicalCloseCount</span></td>
								<td>{{=datasourceNow.PhysicalCloseCount}}</td>
								<td  class="lang"  langKey="PhysicalCloseCountDesc">Close physical connnection count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PhysicalConnectErrorCount">PhysicalConnectErrorCount</span></td>
								<td>{{=datasourceNow.PhysicalConnectErrorCount}}</td>
								<td  class="lang"  langKey="PhysicalConnectErrorCountDesc">Total physical connect error times</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ExecuteCount">ExecuteCount</span></td>
								<td>{{=datasourceNow.ExecuteCount}}</td>
								<td  class="lang"  langKey="ExecuteCountDesc">Total Execute Count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ExecuteQueryCount">ExecuteQueryCount</span></td>
								<td>{{=datasourceNow.ExecuteQueryCount}}</td>
								<td  class="lang"  langKey="ExecuteQueryCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ExecuteUpdateCount">ExecuteUpdateCount</span></td>
								<td>{{=datasourceNow.ExecuteUpdateCount}}</td>
								<td  class="lang"  langKey="ExecuteUpdateCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ExecuteBatchCount">ExecuteBatchCount</span></td>
								<td>{{=datasourceNow.ExecuteBatchCount}}</td>
								<td  class="lang"  langKey="ExecuteBatchCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ErrorCount">ErrorCount</span></td>
								<td>{{=datasourceNow.ErrorCount}}</td>
								<td  class="lang"  langKey="ErrorCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="CommitCount">CommitCount</span></td>
								<td>{{=datasourceNow.CommitCount}}</td>
								<td  class="lang"  langKey="CommitCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="RollbackCount">RollbackCount</span></td>
								<td>{{=datasourceNow.RollbackCount}}</td>
								<td  class="lang"  langKey="RollbackCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PreparedStatementOpenCount">PSOpenCount</span></td>
								<td>{{=datasourceNow.PreparedStatementOpenCount}}</td>
								<td  class="lang"  langKey="PreparedStatementOpenCount"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PreparedStatementClosedCount">PSClosedCount</span></td>
								<td>{{=datasourceNow.PreparedStatementClosedCount}}</td>
								<td  class="lang"  langKey="PreparedStatementClosedCount"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PSCacheAccessCount">PSCacheAccessCount</span></td>
								<td>{{=datasourceNow.PSCacheAccessCount}}</td>
								<td  class="lang"  langKey="PSCacheAccessCountDesc">PerpareStatement access count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PSCacheHitCount">PSCacheHitCount</span></td>
								<td>{{=datasourceNow.PSCacheHitCount}}</td>
								<td  class="lang"  langKey="PSCacheHitCountDesc">PerpareStatement hit count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="PSCacheMissCount">PSCacheMissCount</span></td>
								<td>{{=datasourceNow.PSCacheMissCount}}</td>
								<td  class="lang"  langKey="PSCacheMissCountDesc">PerpareStatement miss count</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ConnectionHoldTimeHistogram">ConnectionHoldTimeHistogram</span></td>
								<td>{{=datasourceNow.ConnectionHoldTimeHistogram}}</td>
								<td  class="lang"  langKey="ConnectionHoldTimeHistogramDesc">The histogram values of connection hold time, [0-1 ms, 1-10 ms, 10-100 ms, 100ms-1s, 1-10 s, 10-100 s, 100-1000 s, >1000 s]</td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="ClobOpenCount">ClobOpenCount</span></td>
								<td>{{=datasourceNow.ClobOpenCount}}</td>
								<td  class="lang"  langKey="ClobOpenCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="BlobOpenCount">BlobOpenCount</span></td>
								<td>{{=datasourceNow.BlobOpenCount}}</td>
								<td  class="lang"  langKey="BlobOpenCountDesc"></td>
							</tr>
							<tr>
								<td valign="top" class="td_lable" >   <span class="lang"  langKey="KeepAliveCheckCount">KeepAliveCheckCount</span></td>
								<td>{{=datasourceNow.KeepAliveCheckCount}}</td>
								<td  class="lang"  langKey="KeepAliveCheckCount"></td>
							</tr>
							<tr>
									<td valign="top" class="td_lable"><span class="lang" langKey="ActiveConnectionStackTrace">ActiveConnection StackTrace</span></td>
									<td>
										<a href="activeConnectionStackTrace.html?datasourceId={{=datasourceNow.Identity}}">View</a>
									</td>
									<td>
										StackTrace for active Connection.
										<a href="activeConnectionStackTrace-{{=datasourceNow.Identity}}.json" 	target="_blank">[View JSON API]</a>
									</td>
								</tr>
								<tr>
									<td valign="top" class="td_lable"><span class="lang" langKey="PollingConnectionInfo">PollingConnection Info</span></td>
									<td>
										<a href="connectionInfo.html?datasourceId={{=datasourceNow.Identity}}&serviceId={{=datasourceNow.serviceId}}">View</a>
									</td>
									<td>
										Info for polling connection. 
										<a href="connectionInfo-{{=datasourceNow.Identity}}.json"	target="_blank">[View JSON API]</a>
									</td>
								</tr>
								<tr>
									<td valign="top" class="td_lable"><span class="lang" langKey="SQLList">SQL List</span></td>
									<td>
										<a href="sql.html?dataSourceId={{=datasourceNow.Identity}}">View</a>
									</td>
									<td>
										Info for SQL.
										<a href="sql.json?dataSourceId={{=datasourceNow.Identity}}" 	target="_blank">[View JSON API]</a>
									</td>
								</tr>
						</tbody>
					</table>
				</div>
				{{~ }}
			</div>
		</script>
    	<script type="text/javascript">
			$.namespace("druid.datasource");
			druid.datasource = function () {  
				
				return  {
					init : function() {
						druid.common.buildHead(1);
						this.ajaxRequestForBasicInfo();
					},
					ajaxRequestForBasicInfo : function() {
						$.ajax({
							type: 'POST',
							url: "datasource.json",
							success: function(data) {

								var tmpl = $('#datasource-tmpl').html();
								var doTtmpl = doT.template(tmpl);
								var contentHtml = doTtmpl(data);
								
								$(".span12 h3").after(contentHtml);
								
								$('#datasourceTab a').click(function (e) {
						            e.preventDefault();
						            $(this).tab('show');
						    	});
						    	druid.lang.trigger();
							},
							dataType: "json"
						});
					}

				}
			}();
	
			$(document).ready(function() {
				druid.datasource.init();
			});
		</script>
	</body>
</html>
