<!doctype html>
<html>
	<head>
		<title>Druid Stat JSON API</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
		<link href='css/bootstrap.min.css' rel="stylesheet" />
		<link href="css/style.css" type="text/css" rel="stylesheet"/>
    	<script type="text/javascript" src="js/jquery.min.js"></script>
		<script src="js/lang.js" type="text/javascript" charset="utf8"></script>
		<script src="js/common.js" type="text/javascript" charset="utf8"></script>
		<script>
			function init() {
				druid.common.buildHead(8);
			}
		</script>
	</head>
	<body onload="init();">
		
		<div class="container">
          				<h3>
          					JSON API
          				</h3>
						<table class="table table-bordered responsive-utilities" style="background-color: #fff">
							<thead>
								<tr>
									<th width="250px" class="td_lable">Name</th>
									<th>Description</th>
								</tr>
							</thead>
							<tr>
								<td class="td_lable"><a href="basic.json" target="_blank">basic.json</a></td>
								<td>basic info.</td>
							</tr>
							<tr>
								<td class="td_lable"><a href="datasource.json" target="_blank">datasource.json</a></td>
								<td>DatsSourceStat List</td>
							</tr>
							<tr>
								<td class="td_lable">datasource-{id}.json</td>
								<td>DatasourceStat for id={id},id is DatsSourceStat.ID<br />
						Example: druid/datasource-1.json</td>
							</tr>
							<tr>
								<td class="td_lable">activeConnectionStackTrace-{datasourceId}.json</td>
								<td>StrackTrace for activeConnection in datasource which datasource.id={datasourceId},id is DatsSourceStat.ID<br />
						Required: set removeAbandoned=true<br />
						Example: druid/activeConnectionStackTrace-1.json</td>
							</tr>
							<tr>
								<td class="td_lable"><a href="sql.json" target="_blank">sql.json</a></td>
								<td>SqlStat for id={id} ,id is SqlStat.ID<br />
						Example: druid/sql-1.json</td>
							</tr>
							<tr>
								<td class="td_lable"><a href="wall.json" target="_blank">WallStat.json</a></td>
								<td>WallStat List</td>
							</tr>
							<tr>
								<td class="td_lable">wall-{id}.json</td>
								<td>WallStat for id={id},id is DatsSourceStat.ID<br />
						Example: druid/wall-1.json</td>
							</tr>
							<tr>
								<td class="td_lable"><a href="basic.json" target="_blank">basic.json</a></td>
								<td></td>
							</tr>
							<tr>
								<td class="td_lable"><a href="weburi.json" target="_blank">weburi.json</a></td>
								<td>
								</td>
							</tr>
							<tr>
								<td class="td_lable"><a href="websession.json" target="_blank">websession.json</a></td>
								<td>
								</td>
							</tr>
							<tr>
								<td class="td_lable"> <a href="reset-all.json" target="_blank">reset-all.json</a> </td>
								<td> Reset all stat </td>
							</tr>
						</table>
          			</div>
	</body>
</html>
