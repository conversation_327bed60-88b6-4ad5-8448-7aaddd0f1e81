<!doctype html>
<html>
<head>
    <title>Druid Spring Stat</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8"/>
    <link href='css/bootstrap.min.css' rel="stylesheet"/>
    <link href="css/style.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script src="js/lang.js" type="text/javascript" charset="utf8"></script>
    <script src="js/common.js" type="text/javascript" charset="utf8"></script>
</head>
<body>
<div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <h3>
                Pooling Connection Info List for Datasource-<span id="datasourceId"></span>
                <span class="pull-right" style="font-size: 16px; margin-right: 20px;">
          						<label langkey="ServiceList" class="lang" style="display: inline;" for="refreshServiceSelect">ServiceList</label>
          						<select id="refreshServiceSelect" class="refresh-seconds-select btn" style="width:200px;"
                                        onchange="javascript:druid.connectionInfo.refreshService=this.options[this.options.selectedIndex].value;">
          						</select>
               	 			</span>
            </h3>
            <table id="dataTable" class="table table-bordered table-striped">
                <thead>
                <tr>
                    <th width="50">ID</th>
                    <th width="50" title="UseCount">UseCnt</th>
                    <th width="50" title="KeepAliveCheckCount">KACheckCnt</th>
                    <th width="150">LastActiveTime</th>
                    <th width="150">ConnectTime</th>
                    <th width="50">Holdability</th>
                    <th width="50" title="Transaction Isolation">TranIsolation</th>
                    <th width="50">AutoCommit</th>
                    <th width="50">ReadOnly</th>
                    <th>
                        Pscache<br/>
                        <table>
                            <thead>
                            <tr>
                                <th>sql</th>
                                <th width="50" title="DefaultRowPrefetch">dfRowPrefetch</th>
                                <th width="50">rowPrefetch</th>
                                <th width="50">hitCount</th>
                            </tr>
                            </thead>
                        </table>
                    </th>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    $.namespace("druid.connectionInfo");
    druid.connectionInfo = function () {
        var datasourceId = druid.common.getUrlVar("datasourceId");
        var serviceId = druid.common.getUrlVar("serviceId");

        return {
            init: function () {
                $("#datasourceId").html(datasourceId);
                druid.common.buildHead(1);
                druid.connectionInfo.ajaxRequestForBasicInfo();
                setInterval("druid.connectionInfo.ajaxRequestForBasicInfo();", 10000);
            },
            refreshService: '',
            ajaxRequestForBasicInfo: function () {
                $.ajax({
                    type: 'POST',
                    url: 'connectionInfo-' + datasourceId + '&serviceId=' + serviceId + '.json',
                    success: function (data) {
                        druid.connectionInfo.handleAjaxResult(data);
                        druid.lang.trigger();
                    },
                    dataType: "json"
                });
            },

            handleAjaxResult: function (data) {
                var statList = data.Content;
                if (statList == null) return;

                var sqlStatTable = document.getElementById("dataTable");
                while (sqlStatTable.rows.length > 1) {
                    sqlStatTable.deleteRow(1);
                }

                var html = "";
                for (var i = 0; i < statList.length; i++) {
                    var stat = statList[i];
                    var newRow = sqlStatTable.insertRow(-1);
                    html += "<tr>";
                    html += "<td>" + stat.connectionId + "</td>";
                    html += "<td>" + stat.useCount + "</td>";
                    html += "<td>" + stat.keepAliveCheckCount + "</td>";

                    if (stat.lastActiveTime)
                        html += "<td>" + stat.lastActiveTime + "</td>";
                    else
                        html += "<td></td>";

                    html += "<td>" + stat.connectTime + "</td>";
                    html += "<td>" + stat.holdability + "</td>";
                    html += "<td>" + stat.transactionIsolation + "</td>";
                    html += "<td>" + stat.autoCommit + "</td>";
                    html += "<td>" + stat.readoOnly + "</td>";
                    if (stat.pscache)
                        html += "<td>" + druid.connectionInfo.getPsCacheInfo(stat.pscache) + "</td>";
                    else
                        html += "<td></td>";
                    html += "</tr>";
                }
                $("#dataTable tbody").html(html);
                druid.common.stripes();
            },

            getPsCacheInfo: function (pscache) {
                var result = '<table cellspacing="1" cellpadding="5" width="100%">';
                for (var i = 0; i < pscache.length; i++) {
                    var stmt = pscache[i];
                    result += '<tr>';
                    result += '<td>' + stmt.sql + "</td>";
                    result += '<td width="50">' + stmt.defaultRowPrefetch + "</td>";
                    result += '<td width="50">' + stmt.rowPrefetch + "</td>";
                    result += '<td width="50">' + stmt.hitCount + "</td>";
                    result += '</tr>';
                }

                result += "</table>";
                return result;
            }
        }
    }();

    $(document).ready(function () {
        druid.connectionInfo.init();
    });
</script>
</body>
</html>
