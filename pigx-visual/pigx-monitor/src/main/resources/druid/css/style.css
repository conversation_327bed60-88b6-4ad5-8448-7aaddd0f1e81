
body{
	font-size:12px;
	padding-top: 60px;
	padding-bottom: 40px;
}
a {
	cursor:pointer;
}
#dataTable th > a {
    color: #555555;
}
#dataTable th {
	background-color: #E5E5E5;
    border-bottom-color: #FFC40D;
    color: #555555;
    cursor: pointer;
}

.footer {
    background-color: #F5F5F5;
    border-top: 1px solid #E5E5E5;
    margin-top: 10px;
    padding: 20px 0;
}

.td_lable {
	font-weight: bold;
	width: 220px;
	background-color: #E5E5E5;
}


.striped {
	background-color: #fff;
}

#dataTable1 th > a {
    color: #555555;
}
#dataTable1 th {
    background-color: #E5E5E5;
    border-bottom-color: #FFC40D;
    color: #555555;
    cursor: pointer;
}


#dataTable2 th > a {
    color: #555555;
}
#dataTable2 th {
    background-color: #E5E5E5;
    border-bottom-color: #FFC40D;
    color: #555555;
    cursor: pointer;
}
