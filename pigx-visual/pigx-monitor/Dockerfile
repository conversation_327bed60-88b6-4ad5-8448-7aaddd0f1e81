FROM registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/java:21-anolis

MAINTAINER <EMAIL>

ENV TZ=Asia/Shanghai
ENV LANG C.UTF-8
ENV JAVA_OPTS="-Xms128m -Xmx256m -Djava.security.egd=file:/dev/./urandom"

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN mkdir -p /pigx-monitor

WORKDIR /pigx-monitor

EXPOSE 5001

ADD ./target/pigx-monitor.jar ./

CMD sleep 120;java $JAVA_OPTS -jar pigx-monitor.jar
