<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.pig4cloud</groupId>
		<artifactId>pigx-visual</artifactId>
		<version>5.7.0</version>
	</parent>

	<artifactId>pigx-mp-platform</artifactId>
	<packaging>jar</packaging>

	<description>微信公众号管理模块</description>
	<properties>
		<mock.version>1.1.2</mock.version>
	</properties>

	<dependencies>
		<!--注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!--数据操作-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-data</artifactId>
		</dependency>
		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
		</dependency>
		<!-- mysql8 -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>
		<!-- ojdbc8 -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
		</dependency>
		<!--PG-->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<!-- druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-3-starter</artifactId>
		</dependency>
		<!--common-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-core</artifactId>
		</dependency>
		<!--安全模块-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-xss</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-security</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-log</artifactId>
		</dependency>
		<!--必备：脱敏工具类-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-sensitive</artifactId>
		</dependency>
		<!-- sentinel-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-sentinel</artifactId>
		</dependency>
		<!--灰度支持-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-gray</artifactId>
		</dependency>
		<!--微信依赖-->
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-mp</artifactId>
		</dependency>
		<!--随机昵称生成-->
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>java-testdata-generator</artifactId>
			<version>${mock.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
	</dependencies>

	<profiles>
		<profile>
			<id>cloud</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-maven-plugin</artifactId>
						<executions>
							<execution>
								<goals>
									<goal>repackage</goal>
								</goals>
								<configuration>
									<loaderImplementation>CLASSIC</loaderImplementation>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>io.fabric8</groupId>
						<artifactId>docker-maven-plugin</artifactId>
						<configuration>
							<skip>false</skip>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>boot</id>
		</profile>
	</profiles>
</project>
