<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.mp.mapper.WxAccountFansMapper">

	<resultMap id="wxAccountFansMap" type="com.pig4cloud.pigx.mp.entity.WxAccountFans">
		<id property="id" column="id"/>
		<result property="openid" column="openid"/>
		<result property="subscribeStatus" column="subscribe_status"/>
		<result property="subscribeTime" column="subscribe_time"/>
		<result property="nickname" column="nickname"/>
		<result property="gender" column="gender"/>
		<result property="language" column="language"/>
		<result property="country" column="country"/>
		<result property="province" column="province"/>
		<result property="city" column="city"/>
		<result property="headimgUrl" column="headimg_url"/>
		<result property="remark" column="remark"/>
		<result property="wxAccountId" column="wx_account_id"/>
		<result property="wxAccountAppid" column="wx_account_appid"/>
		<result property="wxAccountName" column="wx_account_name"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="delFlag" column="del_flag"/>
	</resultMap>
</mapper>
