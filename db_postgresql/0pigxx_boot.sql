-- public.app_article definition

-- Drop table

-- DROP TABLE public.app_article;

CREATE TABLE public.app_article (
	id int8 NOT NULL, -- 主键
	cid int8 NULL, -- 分类
	title varchar(200) NULL, -- 标题
	intro varchar(200) NULL, -- 简介
	summary varchar(200) NULL, -- 摘要
	image varchar(200) NULL, -- 封面
	"content" text NULL, -- 内容
	author varchar(32) NULL, -- 作者
	visit int4 DEFAULT 0 NULL, -- 浏览
	sort int4 DEFAULT 50 NULL, -- 排序
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	create_by varchar(32) NULL, -- 创建人
	update_by varchar(32) NULL, -- 更新人
	del_flag varchar(1) NULL, -- 删除时间
	tenant_id int8 NULL,
	CONSTRAINT app_article_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.app_article IS '文章资讯表';

-- <PERSON>umn comments

COMMENT ON COLUMN public.app_article.id IS '主键';
COMMENT ON COLUMN public.app_article.cid IS '分类';
COMMENT ON COLUMN public.app_article.title IS '标题';
COMMENT ON COLUMN public.app_article.intro IS '简介';
COMMENT ON COLUMN public.app_article.summary IS '摘要';
COMMENT ON COLUMN public.app_article.image IS '封面';
COMMENT ON COLUMN public.app_article."content" IS '内容';
COMMENT ON COLUMN public.app_article.author IS '作者';
COMMENT ON COLUMN public.app_article.visit IS '浏览';
COMMENT ON COLUMN public.app_article.sort IS '排序';
COMMENT ON COLUMN public.app_article.create_time IS '创建时间';
COMMENT ON COLUMN public.app_article.update_time IS '更新时间';
COMMENT ON COLUMN public.app_article.create_by IS '创建人';
COMMENT ON COLUMN public.app_article.update_by IS '更新人';
COMMENT ON COLUMN public.app_article.del_flag IS '删除时间';


-- public.app_article_category definition

-- Drop table

-- DROP TABLE public.app_article_category;

CREATE TABLE public.app_article_category (
	id int8 NOT NULL, -- 主键
	"name" varchar(60) NULL, -- 名称
	sort int2 DEFAULT 50 NULL, -- 排序
	is_show int2 DEFAULT 1 NULL, -- 是否显示: 0=否, 1=是
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 是否删除: 0=否, 1=是
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	create_by varchar(32) DEFAULT '0'::character varying NULL, -- 创建人
	update_by varchar(32) NULL, -- 更新人
	tenant_id int8 NULL,
	CONSTRAINT app_article_category_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.app_article_category IS '文章分类表';

-- Column comments

COMMENT ON COLUMN public.app_article_category.id IS '主键';
COMMENT ON COLUMN public.app_article_category."name" IS '名称';
COMMENT ON COLUMN public.app_article_category.sort IS '排序';
COMMENT ON COLUMN public.app_article_category.is_show IS '是否显示: 0=否, 1=是';
COMMENT ON COLUMN public.app_article_category.del_flag IS '是否删除: 0=否, 1=是';
COMMENT ON COLUMN public.app_article_category.create_time IS '创建时间';
COMMENT ON COLUMN public.app_article_category.update_time IS '更新时间';
COMMENT ON COLUMN public.app_article_category.create_by IS '创建人';
COMMENT ON COLUMN public.app_article_category.update_by IS '更新人';


-- public.app_article_collect definition

-- Drop table

-- DROP TABLE public.app_article_collect;

CREATE TABLE public.app_article_collect (
	id int8 NOT NULL, -- 主键
	user_id int8 DEFAULT 0 NULL, -- 用户ID
	article_id int8 DEFAULT 0 NULL, -- 文章ID
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 是否删除
	create_by varchar(32) NULL, -- 创建人
	update_by varchar(32) NULL, -- 更新人
	tenant_id int8 NULL,
	CONSTRAINT app_article_collect_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.app_article_collect IS '文章收藏表';

-- Column comments

COMMENT ON COLUMN public.app_article_collect.id IS '主键';
COMMENT ON COLUMN public.app_article_collect.user_id IS '用户ID';
COMMENT ON COLUMN public.app_article_collect.article_id IS '文章ID';
COMMENT ON COLUMN public.app_article_collect.create_time IS '创建时间';
COMMENT ON COLUMN public.app_article_collect.update_time IS '更新时间';
COMMENT ON COLUMN public.app_article_collect.del_flag IS '是否删除';
COMMENT ON COLUMN public.app_article_collect.create_by IS '创建人';
COMMENT ON COLUMN public.app_article_collect.update_by IS '更新人';


-- public.app_page definition

-- Drop table

-- DROP TABLE public.app_page;

CREATE TABLE public.app_page (
	id int8 NOT NULL, -- 主键
	page_type int2 DEFAULT 10 NULL, -- 页面类型
	page_name varchar(100) NULL, -- 页面名称
	page_data text NULL, -- 页面数据
	create_by varchar(32) NULL, -- 创建人
	update_by varchar(32) NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) NULL, -- 删除标记
	tenant_id int8 NULL,
	CONSTRAINT app_page_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.app_page IS '页面装修表';

-- Column comments

COMMENT ON COLUMN public.app_page.id IS '主键';
COMMENT ON COLUMN public.app_page.page_type IS '页面类型';
COMMENT ON COLUMN public.app_page.page_name IS '页面名称';
COMMENT ON COLUMN public.app_page.page_data IS '页面数据';
COMMENT ON COLUMN public.app_page.create_by IS '创建人';
COMMENT ON COLUMN public.app_page.update_by IS '修改人';
COMMENT ON COLUMN public.app_page.create_time IS '创建时间';
COMMENT ON COLUMN public.app_page.update_time IS '修改时间';
COMMENT ON COLUMN public.app_page.del_flag IS '删除标记';


-- public.app_role definition

-- Drop table

-- DROP TABLE public.app_role;

CREATE TABLE public.app_role (
	role_id int8 NOT NULL,
	role_name varchar(64) NULL,
	role_code varchar(64) NULL,
	role_desc varchar(255) NULL,
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL,
	update_time timestamp NULL,
	del_flag varchar(1) DEFAULT '0'::character varying NULL,
	tenant_id int8 NULL,
	CONSTRAINT app_role_pkey PRIMARY KEY (role_id)
);
COMMENT ON TABLE public.app_role IS 'app角色表';

-- Column comments

COMMENT ON COLUMN public.app_role.create_by IS '创建人';
COMMENT ON COLUMN public.app_role.update_by IS '修改人';


-- public.app_social_details definition

-- Drop table

-- DROP TABLE public.app_social_details;

CREATE TABLE public.app_social_details (
	id int8 NOT NULL, -- 主键
	"type" varchar(16) NULL, -- 社交类型
	remark varchar(64) NULL, -- 备注
	app_id varchar(64) NULL, -- 应用ID
	app_secret varchar(64) NULL, -- 应用密钥
	redirect_url varchar(128) NULL, -- 重定向URL
	ext varchar(255) NULL, -- 拓展字段
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	CONSTRAINT app_social_details_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.app_social_details IS '系统社交登录账号表';

-- Column comments

COMMENT ON COLUMN public.app_social_details.id IS '主键';
COMMENT ON COLUMN public.app_social_details."type" IS '社交类型';
COMMENT ON COLUMN public.app_social_details.remark IS '备注';
COMMENT ON COLUMN public.app_social_details.app_id IS '应用ID';
COMMENT ON COLUMN public.app_social_details.app_secret IS '应用密钥';
COMMENT ON COLUMN public.app_social_details.redirect_url IS '重定向URL';
COMMENT ON COLUMN public.app_social_details.ext IS '拓展字段';
COMMENT ON COLUMN public.app_social_details.create_by IS '创建人';
COMMENT ON COLUMN public.app_social_details.update_by IS '修改人';
COMMENT ON COLUMN public.app_social_details.create_time IS '创建时间';
COMMENT ON COLUMN public.app_social_details.update_time IS '更新时间';
COMMENT ON COLUMN public.app_social_details.del_flag IS '删除标志';


-- public.app_tabbar definition

-- Drop table

-- DROP TABLE public.app_tabbar;

CREATE TABLE public.app_tabbar (
	id int8 NOT NULL, -- 主键
	"name" varchar(20) NULL, -- 导航名称
	selected varchar(200) NULL, -- 未选图标
	unselected varchar(200) NULL, -- 已选图标
	link varchar(200) NULL, -- 链接地址
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	create_by varchar(32) NULL, -- 创建人
	update_by varchar(32) NULL, -- 更新人
	del_flag varchar(1) NULL, -- 删除标记
	tenant_id int8 NULL,
	CONSTRAINT app_tabbar_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.app_tabbar IS '底部装修表';

-- Column comments

COMMENT ON COLUMN public.app_tabbar.id IS '主键';
COMMENT ON COLUMN public.app_tabbar."name" IS '导航名称';
COMMENT ON COLUMN public.app_tabbar.selected IS '未选图标';
COMMENT ON COLUMN public.app_tabbar.unselected IS '已选图标';
COMMENT ON COLUMN public.app_tabbar.link IS '链接地址';
COMMENT ON COLUMN public.app_tabbar.create_time IS '创建时间';
COMMENT ON COLUMN public.app_tabbar.update_time IS '更新时间';
COMMENT ON COLUMN public.app_tabbar.create_by IS '创建人';
COMMENT ON COLUMN public.app_tabbar.update_by IS '更新人';
COMMENT ON COLUMN public.app_tabbar.del_flag IS '删除标记';


-- public.app_user definition

-- Drop table

-- DROP TABLE public.app_user;

CREATE TABLE public.app_user (
	user_id int8 NOT NULL, -- 用户id
	username varchar(255) NULL, -- 用户名
	"password" varchar(255) NULL, -- 密码
	salt varchar(255) NULL, -- 盐值
	phone varchar(20) NULL, -- 手机号码
	avatar varchar(255) NULL, -- 头像图片链接
	nickname varchar(64) NULL, -- 拓展字段:昵称
	"name" varchar(64) NULL, -- 拓展字段:姓名
	email varchar(128) NULL, -- 拓展字段:邮箱
	create_by varchar(64) NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	last_modified_time timestamp NULL, -- 最后一次密码修改时间
	lock_flag varchar(1) DEFAULT '0'::character varying NULL, -- 锁定状态
	wx_openid varchar(32) NULL, -- 微信登录openId
	CONSTRAINT app_user_pkey PRIMARY KEY (user_id)
);
COMMENT ON TABLE public.app_user IS 'app用户表';

-- Column comments

COMMENT ON COLUMN public.app_user.user_id IS '用户id';
COMMENT ON COLUMN public.app_user.username IS '用户名';
COMMENT ON COLUMN public.app_user."password" IS '密码';
COMMENT ON COLUMN public.app_user.salt IS '盐值';
COMMENT ON COLUMN public.app_user.phone IS '手机号码';
COMMENT ON COLUMN public.app_user.avatar IS '头像图片链接';
COMMENT ON COLUMN public.app_user.nickname IS '拓展字段:昵称';
COMMENT ON COLUMN public.app_user."name" IS '拓展字段:姓名';
COMMENT ON COLUMN public.app_user.email IS '拓展字段:邮箱';
COMMENT ON COLUMN public.app_user.create_by IS '创建人';
COMMENT ON COLUMN public.app_user.update_by IS '修改人';
COMMENT ON COLUMN public.app_user.create_time IS '创建时间';
COMMENT ON COLUMN public.app_user.update_time IS '修改时间';
COMMENT ON COLUMN public.app_user.del_flag IS '删除标志';
COMMENT ON COLUMN public.app_user.tenant_id IS '所属租户id';
COMMENT ON COLUMN public.app_user.last_modified_time IS '最后一次密码修改时间';
COMMENT ON COLUMN public.app_user.lock_flag IS '锁定状态';
COMMENT ON COLUMN public.app_user.wx_openid IS '微信登录openId';


-- public.app_user_role definition

-- Drop table

-- DROP TABLE public.app_user_role;

CREATE TABLE public.app_user_role (
	user_id int8 NOT NULL, -- 用户ID
	role_id int8 NOT NULL, -- 角色ID
	CONSTRAINT app_user_role_pkey PRIMARY KEY (user_id, role_id)
);
COMMENT ON TABLE public.app_user_role IS '用户角色表';

-- Column comments

COMMENT ON COLUMN public.app_user_role.user_id IS '用户ID';
COMMENT ON COLUMN public.app_user_role.role_id IS '角色ID';


-- public.gen_create_table definition

-- Drop table

-- DROP TABLE public.gen_create_table;

CREATE TABLE public.gen_create_table (
	id int8 NOT NULL, -- 主键ID
	table_name varchar(32) NULL, -- 表名称
	ds_name varchar(32) NULL, -- 数据源
	"comments" varchar(512) NULL, -- 表注释
	create_by varchar(64) NULL, -- 创建人
	update_by varchar(64) NULL,
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL,
	column_info text NULL, -- 字段信息
	del_flag varchar(1) NULL, -- 删除标记
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT gen_create_table_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_create_table IS '自动创建表管理';

-- Column comments

COMMENT ON COLUMN public.gen_create_table.id IS '主键ID';
COMMENT ON COLUMN public.gen_create_table.table_name IS '表名称';
COMMENT ON COLUMN public.gen_create_table.ds_name IS '数据源';
COMMENT ON COLUMN public.gen_create_table."comments" IS '表注释';
COMMENT ON COLUMN public.gen_create_table.create_by IS '创建人';
COMMENT ON COLUMN public.gen_create_table.create_time IS '创建时间';
COMMENT ON COLUMN public.gen_create_table.column_info IS '字段信息';
COMMENT ON COLUMN public.gen_create_table.del_flag IS '删除标记';
COMMENT ON COLUMN public.gen_create_table.tenant_id IS '租户ID';


-- public.gen_datasource_conf definition

-- Drop table

-- DROP TABLE public.gen_datasource_conf;

CREATE TABLE public.gen_datasource_conf (
	id int8 NOT NULL, -- 主键
	"name" varchar(64) NULL, -- 别名
	url text NULL, -- jdbcurl
	username varchar(64) NULL, -- 用户名
	"password" varchar(64) NULL, -- 密码
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	tenant_id int8 NULL, -- 租户ID
	ds_type varchar(64) NULL, -- 数据库类型
	conf_type varchar(1) NULL, -- 配置类型
	ds_name varchar(64) NULL, -- 数据库名称
	"instance" varchar(64) NULL, -- 实例
	port int4 NULL, -- 端口
	host varchar(128) NULL, -- 主机
	CONSTRAINT gen_datasource_conf_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_datasource_conf IS '数据源表';

-- Column comments

COMMENT ON COLUMN public.gen_datasource_conf.id IS '主键';
COMMENT ON COLUMN public.gen_datasource_conf."name" IS '别名';
COMMENT ON COLUMN public.gen_datasource_conf.url IS 'jdbcurl';
COMMENT ON COLUMN public.gen_datasource_conf.username IS '用户名';
COMMENT ON COLUMN public.gen_datasource_conf."password" IS '密码';
COMMENT ON COLUMN public.gen_datasource_conf.create_time IS '创建时间';
COMMENT ON COLUMN public.gen_datasource_conf.update_time IS '更新';
COMMENT ON COLUMN public.gen_datasource_conf.del_flag IS '删除标记';
COMMENT ON COLUMN public.gen_datasource_conf.tenant_id IS '租户ID';
COMMENT ON COLUMN public.gen_datasource_conf.ds_type IS '数据库类型';
COMMENT ON COLUMN public.gen_datasource_conf.conf_type IS '配置类型';
COMMENT ON COLUMN public.gen_datasource_conf.ds_name IS '数据库名称';
COMMENT ON COLUMN public.gen_datasource_conf."instance" IS '实例';
COMMENT ON COLUMN public.gen_datasource_conf.port IS '端口';
COMMENT ON COLUMN public.gen_datasource_conf.host IS '主机';


-- public.gen_field_type definition

-- Drop table

-- DROP TABLE public.gen_field_type;

CREATE TABLE public.gen_field_type (
	id int8 NOT NULL, -- id
	column_type varchar(200) NULL, -- 字段类型
	attr_type varchar(200) NULL, -- 属性类型
	package_name varchar(200) NULL, -- 属性包名
	create_time timestamp NULL, -- 创建时间
	create_by varchar(64) NULL, -- 创建人
	update_time timestamp NULL, -- 修改时间
	update_by varchar(64) NULL, -- 修改人
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	CONSTRAINT gen_field_type_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_field_type IS '字段类型管理';

-- Column comments

COMMENT ON COLUMN public.gen_field_type.id IS 'id';
COMMENT ON COLUMN public.gen_field_type.column_type IS '字段类型';
COMMENT ON COLUMN public.gen_field_type.attr_type IS '属性类型';
COMMENT ON COLUMN public.gen_field_type.package_name IS '属性包名';
COMMENT ON COLUMN public.gen_field_type.create_time IS '创建时间';
COMMENT ON COLUMN public.gen_field_type.create_by IS '创建人';
COMMENT ON COLUMN public.gen_field_type.update_time IS '修改时间';
COMMENT ON COLUMN public.gen_field_type.update_by IS '修改人';
COMMENT ON COLUMN public.gen_field_type.del_flag IS '删除标记';


-- public.gen_form_conf definition

-- Drop table

-- DROP TABLE public.gen_form_conf;

CREATE TABLE public.gen_form_conf (
	id int8 NOT NULL, -- ID
	ds_name varchar(64) NULL, -- 数据库名称
	table_name varchar(64) NULL, -- 表名称
	form_info text NULL, -- 表单信息
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL,
	tenant_id int8 NULL, -- 所属租户
	CONSTRAINT gen_form_conf_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_form_conf IS '表单配置';

-- Column comments

COMMENT ON COLUMN public.gen_form_conf.id IS 'ID';
COMMENT ON COLUMN public.gen_form_conf.ds_name IS '数据库名称';
COMMENT ON COLUMN public.gen_form_conf.table_name IS '表名称';
COMMENT ON COLUMN public.gen_form_conf.form_info IS '表单信息';
COMMENT ON COLUMN public.gen_form_conf.create_time IS '创建时间';
COMMENT ON COLUMN public.gen_form_conf.update_time IS '修改时间';
COMMENT ON COLUMN public.gen_form_conf.tenant_id IS '所属租户';


-- public.gen_group definition

-- Drop table

-- DROP TABLE public.gen_group;

CREATE TABLE public.gen_group (
	id int8 NOT NULL,
	group_name varchar(255) NULL, -- 分组名称
	group_desc varchar(255) NULL, -- 分组描述
	tenant_id int8 NULL, -- 租户ID
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建人
	update_time timestamp NULL, -- 修改人
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	CONSTRAINT gen_group_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_group IS '模板分组';

-- Column comments

COMMENT ON COLUMN public.gen_group.group_name IS '分组名称';
COMMENT ON COLUMN public.gen_group.group_desc IS '分组描述';
COMMENT ON COLUMN public.gen_group.tenant_id IS '租户ID';
COMMENT ON COLUMN public.gen_group.create_by IS '创建人';
COMMENT ON COLUMN public.gen_group.update_by IS '修改人';
COMMENT ON COLUMN public.gen_group.create_time IS '创建人';
COMMENT ON COLUMN public.gen_group.update_time IS '修改人';
COMMENT ON COLUMN public.gen_group.del_flag IS '删除标记';


-- public.gen_table definition

-- Drop table

-- DROP TABLE public.gen_table;

CREATE TABLE public.gen_table (
	id int8 NOT NULL, -- id
	table_name varchar(200) NULL, -- 表名
	class_name varchar(200) NULL, -- 类名
	db_type varchar(200) NULL, -- 数据库类型
	table_comment varchar(200) NULL, -- 说明
	author varchar(200) NULL, -- 作者
	email varchar(200) NULL, -- 邮箱
	package_name varchar(200) NULL, -- 项目包名
	"version" varchar(200) NULL, -- 项目版本号
	i18n varchar(1) DEFAULT '0'::character varying NULL, -- 是否生成带有i18n 0 不带有 1带有
	"style" int8 NULL, -- 代码风格
	sync_menu_id int8 NULL, -- 所属菜单ID
	sync_route varchar(1) DEFAULT '0'::character varying NULL, -- 是否自动同步路由
	child_table_name varchar(200) NULL, -- 子表名称
	main_field varchar(200) NULL, -- 主表关联键
	child_field varchar(200) NULL, -- 子表关联键
	generator_type varchar(1) DEFAULT '0'::character varying NULL, -- 生成方式  0：zip压缩包   1：自定义目录
	backend_path varchar(500) NULL, -- 后端生成路径
	frontend_path varchar(500) NULL, -- 前端生成路径
	module_name varchar(200) NULL, -- 模块名
	function_name varchar(200) NULL, -- 功能名
	form_layout int2 NULL, -- 表单布局  1：一列   2：两列
	ds_name varchar(200) NULL, -- 数据源ID
	baseclass_id int8 NULL, -- 基类ID
	create_time timestamp NULL, -- 创建时间
	CONSTRAINT gen_table_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_table IS '代码生成表';

-- Column comments

COMMENT ON COLUMN public.gen_table.id IS 'id';
COMMENT ON COLUMN public.gen_table.table_name IS '表名';
COMMENT ON COLUMN public.gen_table.class_name IS '类名';
COMMENT ON COLUMN public.gen_table.db_type IS '数据库类型';
COMMENT ON COLUMN public.gen_table.table_comment IS '说明';
COMMENT ON COLUMN public.gen_table.author IS '作者';
COMMENT ON COLUMN public.gen_table.email IS '邮箱';
COMMENT ON COLUMN public.gen_table.package_name IS '项目包名';
COMMENT ON COLUMN public.gen_table."version" IS '项目版本号';
COMMENT ON COLUMN public.gen_table.i18n IS '是否生成带有i18n 0 不带有 1带有';
COMMENT ON COLUMN public.gen_table."style" IS '代码风格';
COMMENT ON COLUMN public.gen_table.sync_menu_id IS '所属菜单ID';
COMMENT ON COLUMN public.gen_table.sync_route IS '是否自动同步路由';
COMMENT ON COLUMN public.gen_table.child_table_name IS '子表名称';
COMMENT ON COLUMN public.gen_table.main_field IS '主表关联键';
COMMENT ON COLUMN public.gen_table.child_field IS '子表关联键';
COMMENT ON COLUMN public.gen_table.generator_type IS '生成方式  0：zip压缩包   1：自定义目录';
COMMENT ON COLUMN public.gen_table.backend_path IS '后端生成路径';
COMMENT ON COLUMN public.gen_table.frontend_path IS '前端生成路径';
COMMENT ON COLUMN public.gen_table.module_name IS '模块名';
COMMENT ON COLUMN public.gen_table.function_name IS '功能名';
COMMENT ON COLUMN public.gen_table.form_layout IS '表单布局  1：一列   2：两列';
COMMENT ON COLUMN public.gen_table.ds_name IS '数据源ID';
COMMENT ON COLUMN public.gen_table.baseclass_id IS '基类ID';
COMMENT ON COLUMN public.gen_table.create_time IS '创建时间';


-- public.gen_table_column definition

-- Drop table

-- DROP TABLE public.gen_table_column;

CREATE TABLE public.gen_table_column (
	id int8 NOT NULL, -- id
	ds_name varchar(200) NULL, -- 数据源名称
	table_name varchar(200) NULL, -- 表名称
	field_name varchar(200) NULL, -- 字段名称
	field_type varchar(200) NULL, -- 字段类型
	field_comment varchar(200) NULL, -- 字段说明
	attr_name varchar(200) NULL, -- 属性名
	attr_type varchar(200) NULL, -- 属性类型
	package_name varchar(200) NULL, -- 属性包名
	sort int4 NULL, -- 排序
	auto_fill varchar(20) NULL, -- 自动填充  DEFAULT、INSERT、UPDATE、INSERT_UPDATE
	primary_pk varchar(1) DEFAULT '0'::character varying NULL, -- 主键 0：否  1：是
	base_field varchar(1) DEFAULT '0'::character varying NULL, -- 基类字段 0：否  1：是
	form_item varchar(1) DEFAULT '0'::character varying NULL, -- 表单项 0：否  1：是
	form_required varchar(1) DEFAULT '0'::character varying NULL, -- 表单必填 0：否  1：是
	form_type varchar(200) NULL, -- 表单类型
	form_validator varchar(200) NULL, -- 表单效验
	grid_item varchar(1) DEFAULT '0'::character varying NULL, -- 列表项 0：否  1：是
	grid_sort varchar(1) DEFAULT '0'::character varying NULL, -- 列表排序 0：否  1：是
	query_item varchar(1) DEFAULT '0'::character varying NULL, -- 查询项 0：否  1：是
	query_type varchar(200) NULL, -- 查询方式
	query_form_type varchar(200) NULL, -- 查询表单类型
	field_dict varchar(200) NULL, -- 字典类型
	CONSTRAINT gen_table_column_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_table_column IS '代码生成表字段';

-- Column comments

COMMENT ON COLUMN public.gen_table_column.id IS 'id';
COMMENT ON COLUMN public.gen_table_column.ds_name IS '数据源名称';
COMMENT ON COLUMN public.gen_table_column.table_name IS '表名称';
COMMENT ON COLUMN public.gen_table_column.field_name IS '字段名称';
COMMENT ON COLUMN public.gen_table_column.field_type IS '字段类型';
COMMENT ON COLUMN public.gen_table_column.field_comment IS '字段说明';
COMMENT ON COLUMN public.gen_table_column.attr_name IS '属性名';
COMMENT ON COLUMN public.gen_table_column.attr_type IS '属性类型';
COMMENT ON COLUMN public.gen_table_column.package_name IS '属性包名';
COMMENT ON COLUMN public.gen_table_column.sort IS '排序';
COMMENT ON COLUMN public.gen_table_column.auto_fill IS '自动填充  DEFAULT、INSERT、UPDATE、INSERT_UPDATE';
COMMENT ON COLUMN public.gen_table_column.primary_pk IS '主键 0：否  1：是';
COMMENT ON COLUMN public.gen_table_column.base_field IS '基类字段 0：否  1：是';
COMMENT ON COLUMN public.gen_table_column.form_item IS '表单项 0：否  1：是';
COMMENT ON COLUMN public.gen_table_column.form_required IS '表单必填 0：否  1：是';
COMMENT ON COLUMN public.gen_table_column.form_type IS '表单类型';
COMMENT ON COLUMN public.gen_table_column.form_validator IS '表单效验';
COMMENT ON COLUMN public.gen_table_column.grid_item IS '列表项 0：否  1：是';
COMMENT ON COLUMN public.gen_table_column.grid_sort IS '列表排序 0：否  1：是';
COMMENT ON COLUMN public.gen_table_column.query_item IS '查询项 0：否  1：是';
COMMENT ON COLUMN public.gen_table_column.query_type IS '查询方式';
COMMENT ON COLUMN public.gen_table_column.query_form_type IS '查询表单类型';
COMMENT ON COLUMN public.gen_table_column.field_dict IS '字典类型';


-- public.gen_template definition

-- Drop table

-- DROP TABLE public.gen_template;

CREATE TABLE public.gen_template (
	id int8 NOT NULL, -- 主键
	template_name varchar(255) NULL, -- 模板名称
	generator_path varchar(255) NULL, -- 模板路径
	template_desc varchar(255) NULL, -- 模板描述
	template_code text NULL, -- 模板代码
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	tenant_id int8 NULL, -- 租户ID
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	CONSTRAINT gen_template_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gen_template IS '模板';

-- Column comments

COMMENT ON COLUMN public.gen_template.id IS '主键';
COMMENT ON COLUMN public.gen_template.template_name IS '模板名称';
COMMENT ON COLUMN public.gen_template.generator_path IS '模板路径';
COMMENT ON COLUMN public.gen_template.template_desc IS '模板描述';
COMMENT ON COLUMN public.gen_template.template_code IS '模板代码';
COMMENT ON COLUMN public.gen_template.create_time IS '创建时间';
COMMENT ON COLUMN public.gen_template.update_time IS '更新';
COMMENT ON COLUMN public.gen_template.del_flag IS '删除标记';
COMMENT ON COLUMN public.gen_template.tenant_id IS '租户ID';
COMMENT ON COLUMN public.gen_template.create_by IS '创建人';
COMMENT ON COLUMN public.gen_template.update_by IS '修改人';


-- public.gen_template_group definition

-- Drop table

-- DROP TABLE public.gen_template_group;

CREATE TABLE public.gen_template_group (
	group_id int8 NOT NULL, -- 分组id
	template_id int8 NOT NULL, -- 模板id
	CONSTRAINT gen_template_group_pkey PRIMARY KEY (group_id, template_id)
);
COMMENT ON TABLE public.gen_template_group IS '模板分组关联表';

-- Column comments

COMMENT ON COLUMN public.gen_template_group.group_id IS '分组id';
COMMENT ON COLUMN public.gen_template_group.template_id IS '模板id';


-- public.pay_channel definition

-- Drop table

-- DROP TABLE public.pay_channel;

CREATE TABLE public.pay_channel (
	id int8 NOT NULL, -- 渠道主键ID
	mch_id varchar(32) NULL, -- 商户ID
	channel_id varchar(24) NULL, -- 渠道ID
	channel_name varchar(30) NULL, -- 渠道名称
	channel_mch_id varchar(32) NULL, -- 渠道商户ID
	return_url varchar(255) NULL, -- 前端回调地址
	notify_url varchar(255) NULL, -- 后端回调地址
	state varchar(1) DEFAULT '0'::character varying NULL, -- 状态
	param text NULL, -- 参数
	remark varchar(128) NULL, -- 备注
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	tenant_id int8 NULL, -- 租户ID
	app_id varchar(64) NULL, -- 应用ID
	CONSTRAINT pay_channel_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.pay_channel IS '支付渠道表';

-- Column comments

COMMENT ON COLUMN public.pay_channel.id IS '渠道主键ID';
COMMENT ON COLUMN public.pay_channel.mch_id IS '商户ID';
COMMENT ON COLUMN public.pay_channel.channel_id IS '渠道ID';
COMMENT ON COLUMN public.pay_channel.channel_name IS '渠道名称';
COMMENT ON COLUMN public.pay_channel.channel_mch_id IS '渠道商户ID';
COMMENT ON COLUMN public.pay_channel.return_url IS '前端回调地址';
COMMENT ON COLUMN public.pay_channel.notify_url IS '后端回调地址';
COMMENT ON COLUMN public.pay_channel.state IS '状态';
COMMENT ON COLUMN public.pay_channel.param IS '参数';
COMMENT ON COLUMN public.pay_channel.remark IS '备注';
COMMENT ON COLUMN public.pay_channel.del_flag IS '删除标志';
COMMENT ON COLUMN public.pay_channel.create_time IS '创建时间';
COMMENT ON COLUMN public.pay_channel.update_time IS '更新时间';
COMMENT ON COLUMN public.pay_channel.tenant_id IS '租户ID';
COMMENT ON COLUMN public.pay_channel.app_id IS '应用ID';


-- public.pay_goods_order definition

-- Drop table

-- DROP TABLE public.pay_goods_order;

CREATE TABLE public.pay_goods_order (
	goods_order_id int8 NOT NULL, -- 商品订单ID
	goods_id varchar(30) NULL, -- 商品ID
	goods_name varchar(64) NULL, -- 商品名称
	amount varchar(20) NULL, -- 金额
	user_id varchar(30) NULL, -- 用户ID
	status varchar(4) DEFAULT '0'::character varying NULL, -- 订单状态：订单生成(0)、支付成功(1)、处理完成(2)、处理失败(-1)
	pay_order_id varchar(30) NULL, -- 支付订单ID
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT pay_goods_order_pkey PRIMARY KEY (goods_order_id)
);
COMMENT ON TABLE public.pay_goods_order IS '商品订单表';

-- Column comments

COMMENT ON COLUMN public.pay_goods_order.goods_order_id IS '商品订单ID';
COMMENT ON COLUMN public.pay_goods_order.goods_id IS '商品ID';
COMMENT ON COLUMN public.pay_goods_order.goods_name IS '商品名称';
COMMENT ON COLUMN public.pay_goods_order.amount IS '金额';
COMMENT ON COLUMN public.pay_goods_order.user_id IS '用户ID';
COMMENT ON COLUMN public.pay_goods_order.status IS '订单状态：订单生成(0)、支付成功(1)、处理完成(2)、处理失败(-1)';
COMMENT ON COLUMN public.pay_goods_order.pay_order_id IS '支付订单ID';
COMMENT ON COLUMN public.pay_goods_order.del_flag IS '删除标志';
COMMENT ON COLUMN public.pay_goods_order.create_time IS '创建时间';
COMMENT ON COLUMN public.pay_goods_order.update_time IS '更新时间';
COMMENT ON COLUMN public.pay_goods_order.tenant_id IS '租户ID';


-- public.pay_notify_record definition

-- Drop table

-- DROP TABLE public.pay_notify_record;

CREATE TABLE public.pay_notify_record (
	id int8 NOT NULL, -- ID
	notify_id varchar(50) NULL, -- 通知ID
	request varchar(2000) NULL, -- 请求内容
	response varchar(2000) NULL, -- 响应内容
	order_no varchar(50) NULL, -- 订单号
	http_status varchar(50) NULL, -- http状态
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT pay_notify_record_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.pay_notify_record IS '通知记录日志表';

-- Column comments

COMMENT ON COLUMN public.pay_notify_record.id IS 'ID';
COMMENT ON COLUMN public.pay_notify_record.notify_id IS '通知ID';
COMMENT ON COLUMN public.pay_notify_record.request IS '请求内容';
COMMENT ON COLUMN public.pay_notify_record.response IS '响应内容';
COMMENT ON COLUMN public.pay_notify_record.order_no IS '订单号';
COMMENT ON COLUMN public.pay_notify_record.http_status IS 'http状态';
COMMENT ON COLUMN public.pay_notify_record.del_flag IS '删除标志';
COMMENT ON COLUMN public.pay_notify_record.create_time IS '创建时间';
COMMENT ON COLUMN public.pay_notify_record.update_time IS '更新时间';
COMMENT ON COLUMN public.pay_notify_record.tenant_id IS '租户ID';


-- public.pay_refund_order definition

-- Drop table

-- DROP TABLE public.pay_refund_order;

CREATE TABLE public.pay_refund_order (
	refund_order_id int8 NOT NULL, -- 退款订单ID
	pay_order_id varchar(30) NULL, -- 支付订单号
	channel_pay_order_no varchar(64) NULL, -- 渠道支付订单号
	mch_id varchar(30) NULL, -- 商户号
	mch_refund_no varchar(30) NULL, -- 商户退款单号
	channel_id varchar(24) NULL, -- 渠道ID
	pay_amount varchar(20) NULL, -- 支付金额
	refund_amount int8 NULL, -- 退款金额,单位分
	currency varchar(3) NULL, -- 币种
	status int2 DEFAULT 0 NULL, -- 退款状态:0-订单生成,1-退款中,2-退款成功,3-退款失败,4-业务处理完成
	"result" int2 DEFAULT 0 NULL, -- 退款结果:0-不确认结果,1-等待手动处理,2-确认成功,3-确认失败
	client_ip varchar(32) NULL, -- 客户端IP
	device varchar(64) NULL, -- 设备信息
	remark varchar(256) NULL, -- 备注信息
	channel_user varchar(32) NULL, -- 渠道用户标识
	username varchar(24) NULL, -- 用户名
	channel_mch_id varchar(32) NULL, -- 渠道商户号
	channel_order_no varchar(32) NULL, -- 渠道订单号
	channel_err_code varchar(128) NULL, -- 渠道错误码
	channel_err_msg varchar(128) NULL, -- 渠道错误信息
	extra varchar(512) NULL, -- 附加信息
	notify_url varchar(128) NULL, -- 通知URL
	param1 varchar(64) NULL, -- 扩展参数1
	param2 varchar(64) NULL, -- 扩展参数2
	expire_time timestamp NULL, -- 订单失效时间
	refund_succ_time timestamp NULL, -- 订单退款成功时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT pay_refund_order_pkey PRIMARY KEY (refund_order_id)
);
COMMENT ON TABLE public.pay_refund_order IS '退款订单表';

-- Column comments

COMMENT ON COLUMN public.pay_refund_order.refund_order_id IS '退款订单ID';
COMMENT ON COLUMN public.pay_refund_order.pay_order_id IS '支付订单号';
COMMENT ON COLUMN public.pay_refund_order.channel_pay_order_no IS '渠道支付订单号';
COMMENT ON COLUMN public.pay_refund_order.mch_id IS '商户号';
COMMENT ON COLUMN public.pay_refund_order.mch_refund_no IS '商户退款单号';
COMMENT ON COLUMN public.pay_refund_order.channel_id IS '渠道ID';
COMMENT ON COLUMN public.pay_refund_order.pay_amount IS '支付金额';
COMMENT ON COLUMN public.pay_refund_order.refund_amount IS '退款金额,单位分';
COMMENT ON COLUMN public.pay_refund_order.currency IS '币种';
COMMENT ON COLUMN public.pay_refund_order.status IS '退款状态:0-订单生成,1-退款中,2-退款成功,3-退款失败,4-业务处理完成';
COMMENT ON COLUMN public.pay_refund_order."result" IS '退款结果:0-不确认结果,1-等待手动处理,2-确认成功,3-确认失败';
COMMENT ON COLUMN public.pay_refund_order.client_ip IS '客户端IP';
COMMENT ON COLUMN public.pay_refund_order.device IS '设备信息';
COMMENT ON COLUMN public.pay_refund_order.remark IS '备注信息';
COMMENT ON COLUMN public.pay_refund_order.channel_user IS '渠道用户标识';
COMMENT ON COLUMN public.pay_refund_order.username IS '用户名';
COMMENT ON COLUMN public.pay_refund_order.channel_mch_id IS '渠道商户号';
COMMENT ON COLUMN public.pay_refund_order.channel_order_no IS '渠道订单号';
COMMENT ON COLUMN public.pay_refund_order.channel_err_code IS '渠道错误码';
COMMENT ON COLUMN public.pay_refund_order.channel_err_msg IS '渠道错误信息';
COMMENT ON COLUMN public.pay_refund_order.extra IS '附加信息';
COMMENT ON COLUMN public.pay_refund_order.notify_url IS '通知URL';
COMMENT ON COLUMN public.pay_refund_order.param1 IS '扩展参数1';
COMMENT ON COLUMN public.pay_refund_order.param2 IS '扩展参数2';
COMMENT ON COLUMN public.pay_refund_order.expire_time IS '订单失效时间';
COMMENT ON COLUMN public.pay_refund_order.refund_succ_time IS '订单退款成功时间';
COMMENT ON COLUMN public.pay_refund_order.del_flag IS '删除标志';
COMMENT ON COLUMN public.pay_refund_order.create_time IS '创建时间';
COMMENT ON COLUMN public.pay_refund_order.update_time IS '更新时间';
COMMENT ON COLUMN public.pay_refund_order.tenant_id IS '租户ID';


-- public.pay_trade_order definition

-- Drop table

-- DROP TABLE public.pay_trade_order;

CREATE TABLE public.pay_trade_order (
	order_id int8 NOT NULL, -- 订单ID
	channel_id varchar(24) NULL, -- 渠道ID
	amount varchar(20) NULL, -- 支付金额
	currency varchar(3) NULL, -- 币种
	status varchar(1) DEFAULT '0'::character varying NULL, -- 支付状态:0-订单生成,1-支付中(目前未使用),2-支付成功,3-业务处理完成
	client_ip varchar(32) NULL, -- 客户端IP
	device varchar(64) NULL, -- 设备信息
	subject varchar(64) NULL, -- 标题
	body varchar(256) NULL, -- 内容
	extra varchar(512) NULL, -- 附加信息
	channel_mch_id varchar(32) NULL, -- 渠道商户号
	channel_order_no varchar(64) NULL, -- 渠道订单号
	err_code varchar(64) NULL, -- 错误码
	err_msg varchar(128) NULL, -- 错误信息
	param1 varchar(64) NULL, -- 扩展参数1
	param2 varchar(64) NULL, -- 扩展参数2
	notify_url varchar(128) NULL, -- 通知URL
	notify_count int2 DEFAULT 0 NULL, -- 通知次数
	last_notify_time int8 NULL, -- 最后一次通知时间
	expire_time int8 NULL, -- 订单失效时间
	pay_succ_time timestamp NULL, -- 订单支付成功时间
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT pay_trade_order_pkey PRIMARY KEY (order_id)
);
COMMENT ON TABLE public.pay_trade_order IS '支付订单表';

-- Column comments

COMMENT ON COLUMN public.pay_trade_order.order_id IS '订单ID';
COMMENT ON COLUMN public.pay_trade_order.channel_id IS '渠道ID';
COMMENT ON COLUMN public.pay_trade_order.amount IS '支付金额';
COMMENT ON COLUMN public.pay_trade_order.currency IS '币种';
COMMENT ON COLUMN public.pay_trade_order.status IS '支付状态:0-订单生成,1-支付中(目前未使用),2-支付成功,3-业务处理完成';
COMMENT ON COLUMN public.pay_trade_order.client_ip IS '客户端IP';
COMMENT ON COLUMN public.pay_trade_order.device IS '设备信息';
COMMENT ON COLUMN public.pay_trade_order.subject IS '标题';
COMMENT ON COLUMN public.pay_trade_order.body IS '内容';
COMMENT ON COLUMN public.pay_trade_order.extra IS '附加信息';
COMMENT ON COLUMN public.pay_trade_order.channel_mch_id IS '渠道商户号';
COMMENT ON COLUMN public.pay_trade_order.channel_order_no IS '渠道订单号';
COMMENT ON COLUMN public.pay_trade_order.err_code IS '错误码';
COMMENT ON COLUMN public.pay_trade_order.err_msg IS '错误信息';
COMMENT ON COLUMN public.pay_trade_order.param1 IS '扩展参数1';
COMMENT ON COLUMN public.pay_trade_order.param2 IS '扩展参数2';
COMMENT ON COLUMN public.pay_trade_order.notify_url IS '通知URL';
COMMENT ON COLUMN public.pay_trade_order.notify_count IS '通知次数';
COMMENT ON COLUMN public.pay_trade_order.last_notify_time IS '最后一次通知时间';
COMMENT ON COLUMN public.pay_trade_order.expire_time IS '订单失效时间';
COMMENT ON COLUMN public.pay_trade_order.pay_succ_time IS '订单支付成功时间';
COMMENT ON COLUMN public.pay_trade_order.create_time IS '创建时间';
COMMENT ON COLUMN public.pay_trade_order.update_time IS '更新时间';
COMMENT ON COLUMN public.pay_trade_order.del_flag IS '删除标志';
COMMENT ON COLUMN public.pay_trade_order.tenant_id IS '租户ID';


-- public.process definition

-- Drop table

-- DROP TABLE public.process;

CREATE TABLE public.process (
	id int8 NOT NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	flow_id varchar(255) NULL, -- 表单ID
	"name" varchar(50) NULL, -- 表单名称
	logo varchar(200) NULL, -- 图标配置
	settings text NULL, -- 设置项
	group_id int8 NULL, -- 分组ID
	form_items text NULL, -- 表单设置内容
	process text NULL, -- 流程设置内容
	remark varchar(125) NULL, -- 备注
	sort int4 NULL,
	is_hidden int2 NULL, -- 0 正常 1=隐藏
	is_stop int2 NULL, -- 0 正常 1=停用 
	admin_id int8 NULL, -- 流程管理员
	unique_id varchar(50) NULL, -- 唯一性id
	admin_list varchar(255) NULL, -- 管理员
	range_show varchar(255) NULL, -- 范围描述显示
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process IS '流程定义数据';

-- Column comments

COMMENT ON COLUMN public.process.id IS '用户id';
COMMENT ON COLUMN public.process.del_flag IS '删除标志';
COMMENT ON COLUMN public.process.create_time IS '创建时间';
COMMENT ON COLUMN public.process.update_time IS '更新时间';
COMMENT ON COLUMN public.process.flow_id IS '表单ID';
COMMENT ON COLUMN public.process."name" IS '表单名称';
COMMENT ON COLUMN public.process.logo IS '图标配置';
COMMENT ON COLUMN public.process.settings IS '设置项';
COMMENT ON COLUMN public.process.group_id IS '分组ID';
COMMENT ON COLUMN public.process.form_items IS '表单设置内容';
COMMENT ON COLUMN public.process.process IS '流程设置内容';
COMMENT ON COLUMN public.process.remark IS '备注';
COMMENT ON COLUMN public.process.is_hidden IS '0 正常 1=隐藏';
COMMENT ON COLUMN public.process.is_stop IS '0 正常 1=停用 ';
COMMENT ON COLUMN public.process.admin_id IS '流程管理员';
COMMENT ON COLUMN public.process.unique_id IS '唯一性id';
COMMENT ON COLUMN public.process.admin_list IS '管理员';
COMMENT ON COLUMN public.process.range_show IS '范围描述显示';
COMMENT ON COLUMN public.process.tenant_id IS '所属租户id';


-- public.process_copy definition

-- Drop table

-- DROP TABLE public.process_copy;

CREATE TABLE public.process_copy (
	id int8 NOT NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	start_time timestamp NULL, --  流程发起时间
	node_time timestamp NULL, -- 当前节点时间
	start_user_id int8 NULL, -- 发起人
	flow_id varchar(255) NULL, -- 流程id
	process_instance_id varchar(255) NULL, -- 实例id
	node_id varchar(255) NULL, -- 节点id
	group_id int8 NULL, -- 分组id
	group_name varchar(255) NULL, -- 分组名称
	process_name varchar(255) NULL, -- 流程名称
	node_name varchar(255) NULL, -- 节点 名称
	form_data text NULL, -- 表单数据
	user_id int8 NULL, -- 抄送人id
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_copy_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process_copy IS '流程抄送数据';

-- Column comments

COMMENT ON COLUMN public.process_copy.id IS '用户id';
COMMENT ON COLUMN public.process_copy.del_flag IS '删除标志';
COMMENT ON COLUMN public.process_copy.create_time IS '创建时间';
COMMENT ON COLUMN public.process_copy.update_time IS '更新时间';
COMMENT ON COLUMN public.process_copy.start_time IS ' 流程发起时间';
COMMENT ON COLUMN public.process_copy.node_time IS '当前节点时间';
COMMENT ON COLUMN public.process_copy.start_user_id IS '发起人';
COMMENT ON COLUMN public.process_copy.flow_id IS '流程id';
COMMENT ON COLUMN public.process_copy.process_instance_id IS '实例id';
COMMENT ON COLUMN public.process_copy.node_id IS '节点id';
COMMENT ON COLUMN public.process_copy.group_id IS '分组id';
COMMENT ON COLUMN public.process_copy.group_name IS '分组名称';
COMMENT ON COLUMN public.process_copy.process_name IS '流程名称';
COMMENT ON COLUMN public.process_copy.node_name IS '节点 名称';
COMMENT ON COLUMN public.process_copy.form_data IS '表单数据';
COMMENT ON COLUMN public.process_copy.user_id IS '抄送人id';
COMMENT ON COLUMN public.process_copy.tenant_id IS '所属租户id';


-- public.process_group definition

-- Drop table

-- DROP TABLE public.process_group;

CREATE TABLE public.process_group (
	id int8 NOT NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	group_name varchar(50) NULL, -- 分组名
	sort int4 DEFAULT 0 NULL, -- 排序
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_group_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process_group IS '流程分组';

-- Column comments

COMMENT ON COLUMN public.process_group.id IS '用户id';
COMMENT ON COLUMN public.process_group.del_flag IS '删除标志';
COMMENT ON COLUMN public.process_group.create_time IS '创建时间';
COMMENT ON COLUMN public.process_group.update_time IS '更新时间';
COMMENT ON COLUMN public.process_group.group_name IS '分组名';
COMMENT ON COLUMN public.process_group.sort IS '排序';
COMMENT ON COLUMN public.process_group.tenant_id IS '所属租户id';


-- public.process_instance_record definition

-- Drop table

-- DROP TABLE public.process_instance_record;

CREATE TABLE public.process_instance_record (
	id int8 NOT NULL, -- 用户id
	"name" varchar(50) NULL, -- 流程名字
	logo varchar(200) NULL, -- 头像
	user_id int8 NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	flow_id varchar(255) NULL, -- 流程id
	process_instance_id varchar(50) NULL, -- 流程实例id
	form_data text NULL, -- 表单数据
	group_id int8 NULL, -- 组id
	group_name varchar(100) NULL, -- 组名称
	status int4 DEFAULT 1 NULL, -- 状态
	end_time timestamp NULL, -- 结束时间
	parent_process_instance_id varchar(50) NULL, -- 上级流程实例id
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_instance_record_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process_instance_record IS '流程记录';

-- Column comments

COMMENT ON COLUMN public.process_instance_record.id IS '用户id';
COMMENT ON COLUMN public.process_instance_record."name" IS '流程名字';
COMMENT ON COLUMN public.process_instance_record.logo IS '头像';
COMMENT ON COLUMN public.process_instance_record.user_id IS '用户id';
COMMENT ON COLUMN public.process_instance_record.del_flag IS '删除标志';
COMMENT ON COLUMN public.process_instance_record.create_time IS '创建时间';
COMMENT ON COLUMN public.process_instance_record.update_time IS '更新时间';
COMMENT ON COLUMN public.process_instance_record.flow_id IS '流程id';
COMMENT ON COLUMN public.process_instance_record.process_instance_id IS '流程实例id';
COMMENT ON COLUMN public.process_instance_record.form_data IS '表单数据';
COMMENT ON COLUMN public.process_instance_record.group_id IS '组id';
COMMENT ON COLUMN public.process_instance_record.group_name IS '组名称';
COMMENT ON COLUMN public.process_instance_record.status IS '状态';
COMMENT ON COLUMN public.process_instance_record.end_time IS '结束时间';
COMMENT ON COLUMN public.process_instance_record.parent_process_instance_id IS '上级流程实例id';
COMMENT ON COLUMN public.process_instance_record.tenant_id IS '所属租户id';


-- public.process_node_data definition

-- Drop table

-- DROP TABLE public.process_node_data;

CREATE TABLE public.process_node_data (
	id int8 NOT NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	flow_id varchar(255) NULL, -- 流程id
	"data" text NULL, -- 表单数据
	node_id varchar(50) NULL,
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_node_data_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process_node_data IS '流程节点数据';

-- Column comments

COMMENT ON COLUMN public.process_node_data.id IS '用户id';
COMMENT ON COLUMN public.process_node_data.del_flag IS '删除标志';
COMMENT ON COLUMN public.process_node_data.create_time IS '创建时间';
COMMENT ON COLUMN public.process_node_data.update_time IS '更新时间';
COMMENT ON COLUMN public.process_node_data.flow_id IS '流程id';
COMMENT ON COLUMN public.process_node_data."data" IS '表单数据';
COMMENT ON COLUMN public.process_node_data.tenant_id IS '所属租户id';


-- public.process_node_record definition

-- Drop table

-- DROP TABLE public.process_node_record;

CREATE TABLE public.process_node_record (
	id int8 NOT NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	flow_id varchar(255) NULL, -- 流程id
	process_instance_id varchar(50) NULL, -- 流程实例id
	"data" text NULL, -- 表单数据
	node_id varchar(50) NULL,
	node_type varchar(50) NULL, -- 节点类型
	node_name varchar(50) NULL, -- 节点名字
	status int4 NULL, -- 节点状态
	start_time timestamp NULL, -- 开始时间
	end_time timestamp NULL, -- 结束时间
	execution_id varchar(255) NULL, -- 执行id
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_node_record_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process_node_record IS '流程节点记录';

-- Column comments

COMMENT ON COLUMN public.process_node_record.id IS '用户id';
COMMENT ON COLUMN public.process_node_record.del_flag IS '删除标志';
COMMENT ON COLUMN public.process_node_record.create_time IS '创建时间';
COMMENT ON COLUMN public.process_node_record.update_time IS '更新时间';
COMMENT ON COLUMN public.process_node_record.flow_id IS '流程id';
COMMENT ON COLUMN public.process_node_record.process_instance_id IS '流程实例id';
COMMENT ON COLUMN public.process_node_record."data" IS '表单数据';
COMMENT ON COLUMN public.process_node_record.node_type IS '节点类型';
COMMENT ON COLUMN public.process_node_record.node_name IS '节点名字';
COMMENT ON COLUMN public.process_node_record.status IS '节点状态';
COMMENT ON COLUMN public.process_node_record.start_time IS '开始时间';
COMMENT ON COLUMN public.process_node_record.end_time IS '结束时间';
COMMENT ON COLUMN public.process_node_record.execution_id IS '执行id';
COMMENT ON COLUMN public.process_node_record.tenant_id IS '所属租户id';


-- public.process_node_record_assign_user definition

-- Drop table

-- DROP TABLE public.process_node_record_assign_user;

CREATE TABLE public.process_node_record_assign_user (
	id int8 NOT NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	flow_id varchar(255) NULL, -- 流程id
	process_instance_id varchar(50) NULL, -- 流程实例id
	"data" text NULL, -- 表单数据
	node_id varchar(50) NULL,
	user_id varchar(50) NULL, --  用户id
	status int4 NULL, -- 节点状态
	start_time timestamp NULL, -- 开始时间
	end_time timestamp NULL, -- 结束时间
	execution_id varchar(255) NULL, -- 执行id
	task_id varchar(255) NULL, --  任务id
	approve_desc varchar(1000) NULL, -- 审批意见
	node_name varchar(255) NULL, --  节点名称
	task_type varchar(255) NULL, -- 任务类型
	local_data text NULL, -- 表单本地数据
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_node_record_assign_user_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process_node_record_assign_user IS '流程节点记录-执行人';

-- Column comments

COMMENT ON COLUMN public.process_node_record_assign_user.id IS '用户id';
COMMENT ON COLUMN public.process_node_record_assign_user.del_flag IS '删除标志';
COMMENT ON COLUMN public.process_node_record_assign_user.create_time IS '创建时间';
COMMENT ON COLUMN public.process_node_record_assign_user.update_time IS '更新时间';
COMMENT ON COLUMN public.process_node_record_assign_user.flow_id IS '流程id';
COMMENT ON COLUMN public.process_node_record_assign_user.process_instance_id IS '流程实例id';
COMMENT ON COLUMN public.process_node_record_assign_user."data" IS '表单数据';
COMMENT ON COLUMN public.process_node_record_assign_user.user_id IS ' 用户id';
COMMENT ON COLUMN public.process_node_record_assign_user.status IS '节点状态';
COMMENT ON COLUMN public.process_node_record_assign_user.start_time IS '开始时间';
COMMENT ON COLUMN public.process_node_record_assign_user.end_time IS '结束时间';
COMMENT ON COLUMN public.process_node_record_assign_user.execution_id IS '执行id';
COMMENT ON COLUMN public.process_node_record_assign_user.task_id IS ' 任务id';
COMMENT ON COLUMN public.process_node_record_assign_user.approve_desc IS '审批意见';
COMMENT ON COLUMN public.process_node_record_assign_user.node_name IS ' 节点名称';
COMMENT ON COLUMN public.process_node_record_assign_user.task_type IS '任务类型';
COMMENT ON COLUMN public.process_node_record_assign_user.local_data IS '表单本地数据';
COMMENT ON COLUMN public.process_node_record_assign_user.tenant_id IS '所属租户id';


-- public.process_starter definition

-- Drop table

-- DROP TABLE public.process_starter;

CREATE TABLE public.process_starter (
	id int8 NOT NULL, -- 用户id
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	type_id int8 NULL, -- 用户id或者部门id
	"type" varchar(50) NULL, --  类型 user dept
	process_id int8 NULL, -- 流程id
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户id
	CONSTRAINT process_starter_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.process_starter IS '流程发起人';

-- Column comments

COMMENT ON COLUMN public.process_starter.id IS '用户id';
COMMENT ON COLUMN public.process_starter.del_flag IS '删除标志';
COMMENT ON COLUMN public.process_starter.create_time IS '创建时间';
COMMENT ON COLUMN public.process_starter.update_time IS '更新时间';
COMMENT ON COLUMN public.process_starter.type_id IS '用户id或者部门id';
COMMENT ON COLUMN public.process_starter."type" IS ' 类型 user dept';
COMMENT ON COLUMN public.process_starter.process_id IS '流程id';
COMMENT ON COLUMN public.process_starter.tenant_id IS '所属租户id';


-- public.qrtz_blob_triggers definition

-- Drop table

-- DROP TABLE public.qrtz_blob_triggers;

CREATE TABLE public.qrtz_blob_triggers (
	sched_name varchar(120) NOT NULL,
	trigger_name varchar(200) NOT NULL,
	trigger_group varchar(200) NOT NULL,
	blob_data bytea NULL,
	CONSTRAINT qrtz_blob_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);


-- public.qrtz_calendars definition

-- Drop table

-- DROP TABLE public.qrtz_calendars;

CREATE TABLE public.qrtz_calendars (
	sched_name varchar(120) NOT NULL,
	calendar_name varchar(200) NOT NULL,
	calendar bytea NULL,
	CONSTRAINT qrtz_calendars_pkey PRIMARY KEY (sched_name, calendar_name)
);


-- public.qrtz_cron_triggers definition

-- Drop table

-- DROP TABLE public.qrtz_cron_triggers;

CREATE TABLE public.qrtz_cron_triggers (
	sched_name varchar(120) NOT NULL,
	trigger_name varchar(200) NOT NULL,
	trigger_group varchar(200) NOT NULL,
	cron_expression varchar(200) NULL,
	time_zone_id varchar(80) NULL,
	CONSTRAINT qrtz_cron_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);


-- public.qrtz_fired_triggers definition

-- Drop table

-- DROP TABLE public.qrtz_fired_triggers;

CREATE TABLE public.qrtz_fired_triggers (
	sched_name varchar(120) NOT NULL,
	entry_id varchar(95) NOT NULL,
	trigger_name varchar(200) NULL,
	trigger_group varchar(200) NULL,
	instance_name varchar(200) NULL,
	fired_time int8 NULL,
	sched_time int8 NULL,
	priority int4 NULL,
	state varchar(16) NULL,
	job_name varchar(200) NULL,
	job_group varchar(200) NULL,
	is_nonconcurrent varchar(1) NULL,
	requests_recovery varchar(1) NULL,
	CONSTRAINT qrtz_fired_triggers_pkey PRIMARY KEY (sched_name, entry_id)
);


-- public.qrtz_job_details definition

-- Drop table

-- DROP TABLE public.qrtz_job_details;

CREATE TABLE public.qrtz_job_details (
	sched_name varchar(120) NOT NULL,
	job_name varchar(200) NOT NULL,
	job_group varchar(200) NOT NULL,
	description varchar(250) NULL,
	job_class_name varchar(250) NULL,
	is_durable varchar(1) NULL,
	is_nonconcurrent varchar(1) NULL,
	is_update_data varchar(1) NULL,
	requests_recovery varchar(1) NULL,
	job_data bytea NULL,
	CONSTRAINT qrtz_job_details_pkey PRIMARY KEY (sched_name, job_name, job_group)
);


-- public.qrtz_locks definition

-- Drop table

-- DROP TABLE public.qrtz_locks;

CREATE TABLE public.qrtz_locks (
	sched_name varchar(120) NOT NULL,
	lock_name varchar(40) NOT NULL,
	CONSTRAINT qrtz_locks_pkey PRIMARY KEY (sched_name, lock_name)
);


-- public.qrtz_paused_trigger_grps definition

-- Drop table

-- DROP TABLE public.qrtz_paused_trigger_grps;

CREATE TABLE public.qrtz_paused_trigger_grps (
	sched_name varchar(120) NOT NULL,
	trigger_group varchar(200) NOT NULL,
	CONSTRAINT qrtz_paused_trigger_grps_pkey PRIMARY KEY (sched_name, trigger_group)
);


-- public.qrtz_scheduler_state definition

-- Drop table

-- DROP TABLE public.qrtz_scheduler_state;

CREATE TABLE public.qrtz_scheduler_state (
	sched_name varchar(120) NOT NULL,
	instance_name varchar(200) NOT NULL,
	last_checkin_time int8 NULL,
	checkin_interval int8 NULL,
	CONSTRAINT qrtz_scheduler_state_pkey PRIMARY KEY (sched_name, instance_name)
);


-- public.qrtz_simple_triggers definition

-- Drop table

-- DROP TABLE public.qrtz_simple_triggers;

CREATE TABLE public.qrtz_simple_triggers (
	sched_name varchar(120) NOT NULL,
	trigger_name varchar(200) NOT NULL,
	trigger_group varchar(200) NOT NULL,
	repeat_count int8 NULL,
	repeat_interval int8 NULL,
	times_triggered int8 NULL,
	CONSTRAINT qrtz_simple_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);


-- public.qrtz_simprop_triggers definition

-- Drop table

-- DROP TABLE public.qrtz_simprop_triggers;

CREATE TABLE public.qrtz_simprop_triggers (
	sched_name varchar(120) NOT NULL,
	trigger_name varchar(200) NOT NULL,
	trigger_group varchar(200) NOT NULL,
	str_prop_1 varchar(512) NULL,
	str_prop_2 varchar(512) NULL,
	str_prop_3 varchar(512) NULL,
	int_prop_1 int4 NULL,
	int_prop_2 int4 NULL,
	long_prop_1 int8 NULL,
	long_prop_2 int8 NULL,
	dec_prop_1 numeric(17, 4) NULL,
	dec_prop_2 numeric(17, 4) NULL,
	bool_prop_1 varchar(1) NULL,
	bool_prop_2 varchar(1) NULL,
	CONSTRAINT qrtz_simprop_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);


-- public.qrtz_triggers definition

-- Drop table

-- DROP TABLE public.qrtz_triggers;

CREATE TABLE public.qrtz_triggers (
	sched_name varchar(120) NOT NULL,
	trigger_name varchar(200) NOT NULL,
	trigger_group varchar(200) NOT NULL,
	job_name varchar(200) NULL,
	job_group varchar(200) NULL,
	description varchar(250) NULL,
	next_fire_time int8 NULL,
	prev_fire_time int8 NULL,
	priority int4 NULL,
	trigger_state varchar(16) NULL,
	trigger_type varchar(8) NULL,
	start_time int8 NULL,
	end_time int8 NULL,
	calendar_name varchar(200) NULL,
	misfire_instr int2 NULL,
	job_data bytea NULL,
	CONSTRAINT qrtz_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);


-- public.sys_area definition

-- Drop table

-- DROP TABLE public.sys_area;

CREATE TABLE public.sys_area (
	id int8 NOT NULL, -- 主键ID
	pid int8 DEFAULT 0 NULL, -- 父ID
	"name" varchar(255) NULL, -- 地区名称
	letter varchar(255) NULL, -- 地区字母
	adcode int8 NULL, -- 高德地区code
	"location" varchar(255) NULL, -- 经纬度
	area_sort int8 NULL, -- 排序值
	area_status varchar(1) DEFAULT '1'::character varying NULL, -- 0:未生效，1:生效
	area_type varchar(1) DEFAULT '0'::character varying NULL, -- 0:国家,1:省,2:城市,3:区县
	hot varchar(1) DEFAULT '0'::character varying NULL, -- 0:非热门，1:热门
	city_code varchar(30) NULL, -- 城市编码
	create_by varchar(64) NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	update_by varchar(64) NULL, -- 更新时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	CONSTRAINT sys_area_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_area IS '行政区划表';

-- Column comments

COMMENT ON COLUMN public.sys_area.id IS '主键ID';
COMMENT ON COLUMN public.sys_area.pid IS '父ID';
COMMENT ON COLUMN public.sys_area."name" IS '地区名称';
COMMENT ON COLUMN public.sys_area.letter IS '地区字母';
COMMENT ON COLUMN public.sys_area.adcode IS '高德地区code';
COMMENT ON COLUMN public.sys_area."location" IS '经纬度';
COMMENT ON COLUMN public.sys_area.area_sort IS '排序值';
COMMENT ON COLUMN public.sys_area.area_status IS '0:未生效，1:生效';
COMMENT ON COLUMN public.sys_area.area_type IS '0:国家,1:省,2:城市,3:区县';
COMMENT ON COLUMN public.sys_area.hot IS '0:非热门，1:热门';
COMMENT ON COLUMN public.sys_area.city_code IS '城市编码';
COMMENT ON COLUMN public.sys_area.create_by IS '创建人';
COMMENT ON COLUMN public.sys_area.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_area.update_by IS '更新时间';
COMMENT ON COLUMN public.sys_area.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_area.del_flag IS '删除标记';


-- public.sys_audit_log definition

-- Drop table

-- DROP TABLE public.sys_audit_log;

CREATE TABLE public.sys_audit_log (
	id int8 NOT NULL, -- 主键
	audit_name varchar(255) NULL, -- 审计名称
	audit_field varchar(255) NULL, -- 字段名称
	before_val varchar(255) NULL, -- 变更前值
	after_val varchar(255) NULL, -- 变更后值
	create_by varchar(64) NULL, -- 操作人
	create_time timestamp NULL, -- 操作时间
	del_flag varchar(1) NULL, -- 删除标记
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_audit_log_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_audit_log IS '审计记录表';

-- Column comments

COMMENT ON COLUMN public.sys_audit_log.id IS '主键';
COMMENT ON COLUMN public.sys_audit_log.audit_name IS '审计名称';
COMMENT ON COLUMN public.sys_audit_log.audit_field IS '字段名称';
COMMENT ON COLUMN public.sys_audit_log.before_val IS '变更前值';
COMMENT ON COLUMN public.sys_audit_log.after_val IS '变更后值';
COMMENT ON COLUMN public.sys_audit_log.create_by IS '操作人';
COMMENT ON COLUMN public.sys_audit_log.create_time IS '操作时间';
COMMENT ON COLUMN public.sys_audit_log.del_flag IS '删除标记';
COMMENT ON COLUMN public.sys_audit_log.tenant_id IS '租户ID';


-- public.sys_dept definition

-- Drop table

-- DROP TABLE public.sys_dept;

CREATE TABLE public.sys_dept (
	dept_id int8 NOT NULL, -- 部门ID
	"name" varchar(50) NULL, -- 部门名称
	sort_order int4 DEFAULT 0 NULL, -- 排序
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	parent_id int8 NULL, -- 父级部门ID
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_dept_pkey PRIMARY KEY (dept_id)
);
COMMENT ON TABLE public.sys_dept IS '部门管理';

-- Column comments

COMMENT ON COLUMN public.sys_dept.dept_id IS '部门ID';
COMMENT ON COLUMN public.sys_dept."name" IS '部门名称';
COMMENT ON COLUMN public.sys_dept.sort_order IS '排序';
COMMENT ON COLUMN public.sys_dept.create_by IS '创建人';
COMMENT ON COLUMN public.sys_dept.update_by IS '修改人';
COMMENT ON COLUMN public.sys_dept.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_dept.update_time IS '修改时间';
COMMENT ON COLUMN public.sys_dept.del_flag IS '删除标志';
COMMENT ON COLUMN public.sys_dept.parent_id IS '父级部门ID';
COMMENT ON COLUMN public.sys_dept.tenant_id IS '租户ID';


-- public.sys_dict definition

-- Drop table

-- DROP TABLE public.sys_dict;

CREATE TABLE public.sys_dict (
	id int8 NOT NULL, -- 编号
	dict_type varchar(100) NULL, -- 字典类型
	description varchar(100) NULL, -- 描述
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	remarks varchar(255) NULL, -- 备注信息
	system_flag varchar(1) DEFAULT '0'::character varying NULL, -- 系统标志
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户
	CONSTRAINT sys_dict_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_dict IS '字典表';

-- Column comments

COMMENT ON COLUMN public.sys_dict.id IS '编号';
COMMENT ON COLUMN public.sys_dict.dict_type IS '字典类型';
COMMENT ON COLUMN public.sys_dict.description IS '描述';
COMMENT ON COLUMN public.sys_dict.create_by IS '创建人';
COMMENT ON COLUMN public.sys_dict.update_by IS '修改人';
COMMENT ON COLUMN public.sys_dict.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_dict.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_dict.remarks IS '备注信息';
COMMENT ON COLUMN public.sys_dict.system_flag IS '系统标志';
COMMENT ON COLUMN public.sys_dict.del_flag IS '删除标志';
COMMENT ON COLUMN public.sys_dict.tenant_id IS '所属租户';


-- public.sys_dict_item definition

-- Drop table

-- DROP TABLE public.sys_dict_item;

CREATE TABLE public.sys_dict_item (
	id int8 NOT NULL, -- 编号
	dict_id int8 NULL, -- 字典ID
	item_value varchar(100) NULL, -- 字典项值
	"label" varchar(100) NULL, -- 字典项名称
	dict_type varchar(100) NULL, -- 字典类型
	description varchar(100) NULL, -- 字典项描述
	sort_order int4 DEFAULT 0 NULL, -- 排序（升序）
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	remarks varchar(255) NULL, -- 备注信息
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户
	CONSTRAINT sys_dict_item_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_dict_item IS '字典项';

-- Column comments

COMMENT ON COLUMN public.sys_dict_item.id IS '编号';
COMMENT ON COLUMN public.sys_dict_item.dict_id IS '字典ID';
COMMENT ON COLUMN public.sys_dict_item.item_value IS '字典项值';
COMMENT ON COLUMN public.sys_dict_item."label" IS '字典项名称';
COMMENT ON COLUMN public.sys_dict_item.dict_type IS '字典类型';
COMMENT ON COLUMN public.sys_dict_item.description IS '字典项描述';
COMMENT ON COLUMN public.sys_dict_item.sort_order IS '排序（升序）';
COMMENT ON COLUMN public.sys_dict_item.create_by IS '创建人';
COMMENT ON COLUMN public.sys_dict_item.update_by IS '修改人';
COMMENT ON COLUMN public.sys_dict_item.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_dict_item.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_dict_item.remarks IS '备注信息';
COMMENT ON COLUMN public.sys_dict_item.del_flag IS '删除标志';
COMMENT ON COLUMN public.sys_dict_item.tenant_id IS '所属租户';


-- public.sys_file definition

-- Drop table

-- DROP TABLE public.sys_file;

CREATE TABLE public.sys_file (
	id int8 NOT NULL, -- 编号
	group_id int8 NULL, -- 文件组
	file_name varchar(100) NULL, -- 文件名
	bucket_name varchar(200) NULL, -- 文件存储桶名称
	dir varchar(200) NULL, -- 文件夹名称
	original varchar(100) NULL, -- 原始文件名
	"type" varchar(50) NULL, -- 文件类型
	hash varchar(50) NULL, -- 文件hash
	file_size int8 NULL, -- 文件大小
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 上传时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	tenant_id int8 NULL, -- 所属租户
	CONSTRAINT sys_file_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_file IS '文件管理表';

-- Column comments

COMMENT ON COLUMN public.sys_file.id IS '编号';
COMMENT ON COLUMN public.sys_file.group_id IS '文件组';
COMMENT ON COLUMN public.sys_file.file_name IS '文件名';
COMMENT ON COLUMN public.sys_file.bucket_name IS '文件存储桶名称';
COMMENT ON COLUMN public.sys_file.dir IS '文件夹名称';
COMMENT ON COLUMN public.sys_file.original IS '原始文件名';
COMMENT ON COLUMN public.sys_file."type" IS '文件类型';
COMMENT ON COLUMN public.sys_file.hash IS '文件hash';
COMMENT ON COLUMN public.sys_file.file_size IS '文件大小';
COMMENT ON COLUMN public.sys_file.create_by IS '创建人';
COMMENT ON COLUMN public.sys_file.update_by IS '修改人';
COMMENT ON COLUMN public.sys_file.create_time IS '上传时间';
COMMENT ON COLUMN public.sys_file.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_file.del_flag IS '删除标志';
COMMENT ON COLUMN public.sys_file.tenant_id IS '所属租户';


-- public.sys_file_group definition

-- Drop table

-- DROP TABLE public.sys_file_group;

CREATE TABLE public.sys_file_group (
	id int8 NOT NULL, -- 主键ID
	"type" int2 DEFAULT 10 NULL, -- 类型: [10=图片, 20=视频]
	"name" varchar(32) NULL, -- 分类名称
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	create_by varchar(64) NULL, -- 创建人
	update_by varchar(64) NULL, -- 修改人
	tenant_id int8 NULL, -- 租户
	pid int8 NULL, -- 父ID
	CONSTRAINT sys_file_group_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_file_group IS '文件分类表';

-- Column comments

COMMENT ON COLUMN public.sys_file_group.id IS '主键ID';
COMMENT ON COLUMN public.sys_file_group."type" IS '类型: [10=图片, 20=视频]';
COMMENT ON COLUMN public.sys_file_group."name" IS '分类名称';
COMMENT ON COLUMN public.sys_file_group.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_file_group.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_file_group.del_flag IS '删除标记';
COMMENT ON COLUMN public.sys_file_group.create_by IS '创建人';
COMMENT ON COLUMN public.sys_file_group.update_by IS '修改人';
COMMENT ON COLUMN public.sys_file_group.tenant_id IS '租户';
COMMENT ON COLUMN public.sys_file_group.pid IS '父ID';


-- public.sys_i18n definition

-- Drop table

-- DROP TABLE public.sys_i18n;

CREATE TABLE public.sys_i18n (
	id int8 NOT NULL, -- id
	"name" varchar(255) NULL, -- name
	zh_cn varchar(255) NULL, -- 中文
	en varchar(255) NULL, -- 英文
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	CONSTRAINT sys_i18n_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_i18n IS '系统表-国际化';

-- Column comments

COMMENT ON COLUMN public.sys_i18n.id IS 'id';
COMMENT ON COLUMN public.sys_i18n."name" IS 'name';
COMMENT ON COLUMN public.sys_i18n.zh_cn IS '中文';
COMMENT ON COLUMN public.sys_i18n.en IS '英文';
COMMENT ON COLUMN public.sys_i18n.create_by IS '创建人';
COMMENT ON COLUMN public.sys_i18n.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_i18n.update_by IS '修改人';
COMMENT ON COLUMN public.sys_i18n.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_i18n.del_flag IS '删除标记';


-- public.sys_job definition

-- Drop table

-- DROP TABLE public.sys_job;

CREATE TABLE public.sys_job (
	job_id int8 NOT NULL, -- 任务id
	job_name varchar(64) NOT NULL, -- 任务名称
	job_group varchar(64) NOT NULL, -- 任务组名
	job_order varchar(1) DEFAULT '1'::character varying NULL, -- 组内执行顺利，值越大执行优先级越高，最大值9，最小值1
	job_type varchar(1) DEFAULT '1'::character varying NULL, -- 1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他
	execute_path varchar(500) NULL, -- job_type=3时，rest调用地址，仅支持rest get协议,需要增加String返回值，0成功，1失败;job_type=4时，jar路径;其它值为空
	class_name varchar(500) NULL, -- job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空
	method_name varchar(500) NULL, -- 任务方法
	method_params_value varchar(2000) NULL, -- 参数值
	cron_expression varchar(255) NULL, -- cron执行表达式
	misfire_policy varchar(20) DEFAULT '3'::character varying NULL, -- 错失执行策略（1错失周期立即执行 2错失周期执行一次 3下周期执行）
	job_tenant_type varchar(1) DEFAULT '1'::character varying NULL, -- 1、多租户任务;2、非多租户任务
	job_status varchar(1) DEFAULT '0'::character varying NULL, -- 状态（1、未发布;2、运行中;3、暂停;4、删除;）
	job_execute_status varchar(1) DEFAULT '0'::character varying NULL, -- 状态（0正常 1异常）
	create_by varchar(64) NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	update_by varchar(64) NULL, -- 更新者
	update_time timestamp NULL, -- 更新时间
	start_time timestamp NULL, -- 初次执行时间
	previous_time timestamp NULL, -- 上次执行时间
	next_time timestamp NULL, -- 下次执行时间
	tenant_id int8 DEFAULT 1 NULL, -- 租户
	remark varchar(500) NULL, -- 备注信息
	CONSTRAINT sys_job_pkey PRIMARY KEY (job_id, job_name, job_group)
);
COMMENT ON TABLE public.sys_job IS '定时任务调度表';

-- Column comments

COMMENT ON COLUMN public.sys_job.job_id IS '任务id';
COMMENT ON COLUMN public.sys_job.job_name IS '任务名称';
COMMENT ON COLUMN public.sys_job.job_group IS '任务组名';
COMMENT ON COLUMN public.sys_job.job_order IS '组内执行顺利，值越大执行优先级越高，最大值9，最小值1';
COMMENT ON COLUMN public.sys_job.job_type IS '1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他';
COMMENT ON COLUMN public.sys_job.execute_path IS 'job_type=3时，rest调用地址，仅支持rest get协议,需要增加String返回值，0成功，1失败;job_type=4时，jar路径;其它值为空';
COMMENT ON COLUMN public.sys_job.class_name IS 'job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空';
COMMENT ON COLUMN public.sys_job.method_name IS '任务方法';
COMMENT ON COLUMN public.sys_job.method_params_value IS '参数值';
COMMENT ON COLUMN public.sys_job.cron_expression IS 'cron执行表达式';
COMMENT ON COLUMN public.sys_job.misfire_policy IS '错失执行策略（1错失周期立即执行 2错失周期执行一次 3下周期执行）';
COMMENT ON COLUMN public.sys_job.job_tenant_type IS '1、多租户任务;2、非多租户任务';
COMMENT ON COLUMN public.sys_job.job_status IS '状态（1、未发布;2、运行中;3、暂停;4、删除;）';
COMMENT ON COLUMN public.sys_job.job_execute_status IS '状态（0正常 1异常）';
COMMENT ON COLUMN public.sys_job.create_by IS '创建者';
COMMENT ON COLUMN public.sys_job.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_job.update_by IS '更新者';
COMMENT ON COLUMN public.sys_job.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_job.start_time IS '初次执行时间';
COMMENT ON COLUMN public.sys_job.previous_time IS '上次执行时间';
COMMENT ON COLUMN public.sys_job.next_time IS '下次执行时间';
COMMENT ON COLUMN public.sys_job.tenant_id IS '租户';
COMMENT ON COLUMN public.sys_job.remark IS '备注信息';


-- public.sys_job_log definition

-- Drop table

-- DROP TABLE public.sys_job_log;

CREATE TABLE public.sys_job_log (
	job_log_id int8 NOT NULL, -- 任务日志ID
	job_id int8 NULL, -- 任务id
	job_name varchar(64) NULL, -- 任务名称
	job_group varchar(64) NULL, -- 任务组名
	job_order varchar(1) NULL, -- 组内执行顺利，值越大执行优先级越高，最大值9，最小值1
	job_type varchar(1) DEFAULT '1'::character varying NULL, -- 1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他
	execute_path varchar(500) NULL, -- job_type=3时，rest调用地址，仅支持post协议;job_type=4时，jar路径;其它值为空
	class_name varchar(500) NULL, -- job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空
	method_name varchar(500) NULL, -- 任务方法
	method_params_value varchar(2000) NULL, -- 参数值
	cron_expression varchar(255) NULL, -- cron执行表达式
	job_message varchar(500) NULL, -- 日志信息
	job_log_status varchar(1) DEFAULT '0'::character varying NULL, -- 执行状态（0正常 1失败）
	execute_time varchar(30) NULL, -- 执行时间
	exception_info varchar(2000) NULL, -- 异常信息
	create_time timestamp NULL, -- 创建时间
	tenant_id int8 DEFAULT 1 NULL, -- 租户id
	CONSTRAINT sys_job_log_pkey PRIMARY KEY (job_log_id)
);
COMMENT ON TABLE public.sys_job_log IS '定时任务执行日志表';

-- Column comments

COMMENT ON COLUMN public.sys_job_log.job_log_id IS '任务日志ID';
COMMENT ON COLUMN public.sys_job_log.job_id IS '任务id';
COMMENT ON COLUMN public.sys_job_log.job_name IS '任务名称';
COMMENT ON COLUMN public.sys_job_log.job_group IS '任务组名';
COMMENT ON COLUMN public.sys_job_log.job_order IS '组内执行顺利，值越大执行优先级越高，最大值9，最小值1';
COMMENT ON COLUMN public.sys_job_log.job_type IS '1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他';
COMMENT ON COLUMN public.sys_job_log.execute_path IS 'job_type=3时，rest调用地址，仅支持post协议;job_type=4时，jar路径;其它值为空';
COMMENT ON COLUMN public.sys_job_log.class_name IS 'job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空';
COMMENT ON COLUMN public.sys_job_log.method_name IS '任务方法';
COMMENT ON COLUMN public.sys_job_log.method_params_value IS '参数值';
COMMENT ON COLUMN public.sys_job_log.cron_expression IS 'cron执行表达式';
COMMENT ON COLUMN public.sys_job_log.job_message IS '日志信息';
COMMENT ON COLUMN public.sys_job_log.job_log_status IS '执行状态（0正常 1失败）';
COMMENT ON COLUMN public.sys_job_log.execute_time IS '执行时间';
COMMENT ON COLUMN public.sys_job_log.exception_info IS '异常信息';
COMMENT ON COLUMN public.sys_job_log.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_job_log.tenant_id IS '租户id';


-- public.sys_log definition

-- Drop table

-- DROP TABLE public.sys_log;

CREATE TABLE public.sys_log (
	id int8 NOT NULL, -- 编号
	log_type varchar(1) DEFAULT '0'::character varying NULL, -- 日志类型
	title varchar(255) NULL, -- 日志标题
	service_id varchar(32) NULL, -- 服务ID
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	remote_addr varchar(255) NULL, -- 远程地址
	user_agent varchar(1000) NULL, -- 用户代理
	request_uri varchar(255) NULL, -- 请求URI
	"method" varchar(10) NULL, -- 请求方法
	params text NULL, -- 请求参数
	"time" int8 NULL, -- 执行时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志
	"exception" text NULL, -- 异常信息
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户
	CONSTRAINT sys_log_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_log IS '日志表';

-- Column comments

COMMENT ON COLUMN public.sys_log.id IS '编号';
COMMENT ON COLUMN public.sys_log.log_type IS '日志类型';
COMMENT ON COLUMN public.sys_log.title IS '日志标题';
COMMENT ON COLUMN public.sys_log.service_id IS '服务ID';
COMMENT ON COLUMN public.sys_log.create_by IS '创建人';
COMMENT ON COLUMN public.sys_log.update_by IS '修改人';
COMMENT ON COLUMN public.sys_log.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_log.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_log.remote_addr IS '远程地址';
COMMENT ON COLUMN public.sys_log.user_agent IS '用户代理';
COMMENT ON COLUMN public.sys_log.request_uri IS '请求URI';
COMMENT ON COLUMN public.sys_log."method" IS '请求方法';
COMMENT ON COLUMN public.sys_log.params IS '请求参数';
COMMENT ON COLUMN public.sys_log."time" IS '执行时间';
COMMENT ON COLUMN public.sys_log.del_flag IS '删除标志';
COMMENT ON COLUMN public.sys_log."exception" IS '异常信息';
COMMENT ON COLUMN public.sys_log.tenant_id IS '所属租户';


-- public.sys_menu definition

-- Drop table

-- DROP TABLE public.sys_menu;

CREATE TABLE public.sys_menu (
	menu_id int8 NOT NULL, -- 菜单ID
	"name" varchar(32) NULL, -- 菜单名称
	"permission" varchar(32) NULL, -- 权限标识
	"path" varchar(128) NULL, -- 路由路径
	component varchar(255) NULL, -- 组件
	parent_id int8 NULL, -- 父菜单ID
	icon varchar(64) NULL, -- 菜单图标
	visible varchar(1) DEFAULT '1'::character varying NULL, -- 是否可见，0隐藏，1显示
	sort_order int4 DEFAULT 1 NULL, -- 排序值，越小越靠前
	keep_alive varchar(1) DEFAULT '0'::character varying NULL, -- 是否缓存，0否，1是
	embedded varchar(1) NULL, -- 是否内嵌，0否，1是
	menu_type varchar(1) DEFAULT '0'::character varying NULL, -- 菜单类型，0目录，1菜单，2按钮
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标志，0未删除，1已删除
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_menu_pkey PRIMARY KEY (menu_id)
);
COMMENT ON TABLE public.sys_menu IS '菜单权限表';

-- Column comments

COMMENT ON COLUMN public.sys_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN public.sys_menu."name" IS '菜单名称';
COMMENT ON COLUMN public.sys_menu."permission" IS '权限标识';
COMMENT ON COLUMN public.sys_menu."path" IS '路由路径';
COMMENT ON COLUMN public.sys_menu.component IS '组件';
COMMENT ON COLUMN public.sys_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN public.sys_menu.icon IS '菜单图标';
COMMENT ON COLUMN public.sys_menu.visible IS '是否可见，0隐藏，1显示';
COMMENT ON COLUMN public.sys_menu.sort_order IS '排序值，越小越靠前';
COMMENT ON COLUMN public.sys_menu.keep_alive IS '是否缓存，0否，1是';
COMMENT ON COLUMN public.sys_menu.embedded IS '是否内嵌，0否，1是';
COMMENT ON COLUMN public.sys_menu.menu_type IS '菜单类型，0目录，1菜单，2按钮';
COMMENT ON COLUMN public.sys_menu.create_by IS '创建人';
COMMENT ON COLUMN public.sys_menu.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_menu.update_by IS '修改人';
COMMENT ON COLUMN public.sys_menu.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_menu.del_flag IS '删除标志，0未删除，1已删除';
COMMENT ON COLUMN public.sys_menu.tenant_id IS '租户ID';


-- public.sys_message definition

-- Drop table

-- DROP TABLE public.sys_message;

CREATE TABLE public.sys_message (
	id int8 NOT NULL, -- 主键
	category varchar(255) NULL, -- 分类
	title varchar(255) NULL, -- 标题
	"content" text NULL, -- 内容
	send_flag varchar(1) DEFAULT '0'::character varying NULL, -- 是否推送
	all_flag varchar(1) DEFAULT '0'::character varying NULL, -- 全部接受
	sort int4 DEFAULT 0 NULL, -- 排序 （越大越在前）
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	create_by varchar(32) NULL, -- 创建人
	update_by varchar(32) NULL, -- 更新人
	del_flag varchar(1) NULL, -- 删除时间
	tenant_id int8 NULL, -- 租户
	CONSTRAINT sys_message_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_message IS '站内信息';

-- Column comments

COMMENT ON COLUMN public.sys_message.id IS '主键';
COMMENT ON COLUMN public.sys_message.category IS '分类';
COMMENT ON COLUMN public.sys_message.title IS '标题';
COMMENT ON COLUMN public.sys_message."content" IS '内容';
COMMENT ON COLUMN public.sys_message.send_flag IS '是否推送';
COMMENT ON COLUMN public.sys_message.all_flag IS '全部接受';
COMMENT ON COLUMN public.sys_message.sort IS '排序 （越大越在前）';
COMMENT ON COLUMN public.sys_message.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_message.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_message.create_by IS '创建人';
COMMENT ON COLUMN public.sys_message.update_by IS '更新人';
COMMENT ON COLUMN public.sys_message.del_flag IS '删除时间';
COMMENT ON COLUMN public.sys_message.tenant_id IS '租户';


-- public.sys_message_relation definition

-- Drop table

-- DROP TABLE public.sys_message_relation;

CREATE TABLE public.sys_message_relation (
	id int8 NOT NULL, -- 主键
	msg_id int8 NULL, -- 消息ID
	user_id int8 NULL, -- 接收人ID
	"content" text NULL, -- 内容
	read_flag varchar(1) DEFAULT '0'::character varying NULL, -- 已读（0否，1是）
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	create_by varchar(32) NULL, -- 创建人
	update_by varchar(32) NULL, -- 更新人
	del_flag varchar(1) NULL, -- 删除时间
	tenant_id int8 NULL, -- 租户
	CONSTRAINT sys_message_relation_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_message_relation IS '系统消息推送记录';

-- Column comments

COMMENT ON COLUMN public.sys_message_relation.id IS '主键';
COMMENT ON COLUMN public.sys_message_relation.msg_id IS '消息ID';
COMMENT ON COLUMN public.sys_message_relation.user_id IS '接收人ID';
COMMENT ON COLUMN public.sys_message_relation."content" IS '内容';
COMMENT ON COLUMN public.sys_message_relation.read_flag IS '已读（0否，1是）';
COMMENT ON COLUMN public.sys_message_relation.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_message_relation.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_message_relation.create_by IS '创建人';
COMMENT ON COLUMN public.sys_message_relation.update_by IS '更新人';
COMMENT ON COLUMN public.sys_message_relation.del_flag IS '删除时间';
COMMENT ON COLUMN public.sys_message_relation.tenant_id IS '租户';


-- public.sys_oauth_client_details definition

-- Drop table

-- DROP TABLE public.sys_oauth_client_details;

CREATE TABLE public.sys_oauth_client_details (
	id int8 NOT NULL, -- ID
	client_id varchar(32) NULL, -- 客户端ID
	resource_ids varchar(256) NULL, -- 资源ID集合
	client_secret varchar(256) NULL, -- 客户端秘钥
	"scope" varchar(256) NULL, -- 授权范围
	authorized_grant_types varchar(256) NULL, -- 授权类型
	web_server_redirect_uri varchar(256) NULL, -- 回调地址
	authorities varchar(256) NULL, -- 权限集合
	access_token_validity int4 NULL, -- 访问令牌有效期（秒）
	refresh_token_validity int4 NULL, -- 刷新令牌有效期（秒）
	additional_information varchar(4096) NULL, -- 附加信息
	autoapprove varchar(256) NULL, -- 自动授权
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户ID
	CONSTRAINT sys_oauth_client_details_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_oauth_client_details IS '终端信息表';

-- Column comments

COMMENT ON COLUMN public.sys_oauth_client_details.id IS 'ID';
COMMENT ON COLUMN public.sys_oauth_client_details.client_id IS '客户端ID';
COMMENT ON COLUMN public.sys_oauth_client_details.resource_ids IS '资源ID集合';
COMMENT ON COLUMN public.sys_oauth_client_details.client_secret IS '客户端秘钥';
COMMENT ON COLUMN public.sys_oauth_client_details."scope" IS '授权范围';
COMMENT ON COLUMN public.sys_oauth_client_details.authorized_grant_types IS '授权类型';
COMMENT ON COLUMN public.sys_oauth_client_details.web_server_redirect_uri IS '回调地址';
COMMENT ON COLUMN public.sys_oauth_client_details.authorities IS '权限集合';
COMMENT ON COLUMN public.sys_oauth_client_details.access_token_validity IS '访问令牌有效期（秒）';
COMMENT ON COLUMN public.sys_oauth_client_details.refresh_token_validity IS '刷新令牌有效期（秒）';
COMMENT ON COLUMN public.sys_oauth_client_details.additional_information IS '附加信息';
COMMENT ON COLUMN public.sys_oauth_client_details.autoapprove IS '自动授权';
COMMENT ON COLUMN public.sys_oauth_client_details.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.sys_oauth_client_details.create_by IS '创建人';
COMMENT ON COLUMN public.sys_oauth_client_details.update_by IS '修改人';
COMMENT ON COLUMN public.sys_oauth_client_details.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_oauth_client_details.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_oauth_client_details.tenant_id IS '所属租户ID';


-- public.sys_post definition

-- Drop table

-- DROP TABLE public.sys_post;

CREATE TABLE public.sys_post (
	post_id int8 NOT NULL, -- 岗位ID
	post_code varchar(64) NULL, -- 岗位编码
	post_name varchar(50) NULL, -- 岗位名称
	post_sort int4 NULL, -- 岗位排序
	remark varchar(500) NULL, -- 岗位描述
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 是否删除  -1：已删除  0：正常
	create_time timestamp NULL, -- 创建时间
	create_by varchar(64) NULL, -- 创建人
	update_time timestamp NULL, -- 更新时间
	update_by varchar(64) NULL, -- 更新人
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_post_pkey PRIMARY KEY (post_id)
);
COMMENT ON TABLE public.sys_post IS '岗位信息表';

-- Column comments

COMMENT ON COLUMN public.sys_post.post_id IS '岗位ID';
COMMENT ON COLUMN public.sys_post.post_code IS '岗位编码';
COMMENT ON COLUMN public.sys_post.post_name IS '岗位名称';
COMMENT ON COLUMN public.sys_post.post_sort IS '岗位排序';
COMMENT ON COLUMN public.sys_post.remark IS '岗位描述';
COMMENT ON COLUMN public.sys_post.del_flag IS '是否删除  -1：已删除  0：正常';
COMMENT ON COLUMN public.sys_post.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_post.create_by IS '创建人';
COMMENT ON COLUMN public.sys_post.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_post.update_by IS '更新人';
COMMENT ON COLUMN public.sys_post.tenant_id IS '租户ID';


-- public.sys_public_param definition

-- Drop table

-- DROP TABLE public.sys_public_param;

CREATE TABLE public.sys_public_param (
	public_id int8 NOT NULL, -- 编号
	public_name varchar(128) NULL, -- 名称
	public_key varchar(128) NULL, -- 键
	public_value varchar(128) NULL, -- 值
	status varchar(1) DEFAULT '0'::character varying NULL, -- 状态，0禁用，1启用
	validate_code varchar(64) NULL, -- 校验码
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	public_type varchar(1) DEFAULT '0'::character varying NULL, -- 类型，0未知，1系统，2业务
	system_flag varchar(1) DEFAULT '0'::character varying NULL, -- 系统标识，0非系统，1系统
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_public_param_pkey PRIMARY KEY (public_id)
);
COMMENT ON TABLE public.sys_public_param IS '公共参数配置表';

-- Column comments

COMMENT ON COLUMN public.sys_public_param.public_id IS '编号';
COMMENT ON COLUMN public.sys_public_param.public_name IS '名称';
COMMENT ON COLUMN public.sys_public_param.public_key IS '键';
COMMENT ON COLUMN public.sys_public_param.public_value IS '值';
COMMENT ON COLUMN public.sys_public_param.status IS '状态，0禁用，1启用';
COMMENT ON COLUMN public.sys_public_param.validate_code IS '校验码';
COMMENT ON COLUMN public.sys_public_param.create_by IS '创建人';
COMMENT ON COLUMN public.sys_public_param.update_by IS '修改人';
COMMENT ON COLUMN public.sys_public_param.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_public_param.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_public_param.public_type IS '类型，0未知，1系统，2业务';
COMMENT ON COLUMN public.sys_public_param.system_flag IS '系统标识，0非系统，1系统';
COMMENT ON COLUMN public.sys_public_param.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.sys_public_param.tenant_id IS '租户ID';


-- public.sys_role definition

-- Drop table

-- DROP TABLE public.sys_role;

CREATE TABLE public.sys_role (
	role_id int8 NOT NULL, -- 角色ID
	role_name varchar(64) NULL, -- 角色名称
	role_code varchar(64) NULL, -- 角色编码
	role_desc varchar(255) NULL, -- 角色描述
	ds_type varchar(1) DEFAULT '2'::character varying NULL, -- 数据权限类型，0全部，1自定义，2本部门及以下，3本部门，4仅本人
	ds_scope varchar(255) NULL, -- 数据权限范围
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_role_pkey PRIMARY KEY (role_id)
);
COMMENT ON TABLE public.sys_role IS '系统角色表';

-- Column comments

COMMENT ON COLUMN public.sys_role.role_id IS '角色ID';
COMMENT ON COLUMN public.sys_role.role_name IS '角色名称';
COMMENT ON COLUMN public.sys_role.role_code IS '角色编码';
COMMENT ON COLUMN public.sys_role.role_desc IS '角色描述';
COMMENT ON COLUMN public.sys_role.ds_type IS '数据权限类型，0全部，1自定义，2本部门及以下，3本部门，4仅本人';
COMMENT ON COLUMN public.sys_role.ds_scope IS '数据权限范围';
COMMENT ON COLUMN public.sys_role.create_by IS '创建人';
COMMENT ON COLUMN public.sys_role.update_by IS '修改人';
COMMENT ON COLUMN public.sys_role.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_role.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_role.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.sys_role.tenant_id IS '租户ID';


-- public.sys_role_menu definition

-- Drop table

-- DROP TABLE public.sys_role_menu;

CREATE TABLE public.sys_role_menu (
	role_id int8 NOT NULL, -- 角色ID
	menu_id int8 NOT NULL, -- 菜单ID
	CONSTRAINT sys_role_menu_pkey PRIMARY KEY (role_id, menu_id)
);
COMMENT ON TABLE public.sys_role_menu IS '角色菜单表';

-- Column comments

COMMENT ON COLUMN public.sys_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN public.sys_role_menu.menu_id IS '菜单ID';


-- public.sys_route_conf definition

-- Drop table

-- DROP TABLE public.sys_route_conf;

CREATE TABLE public.sys_route_conf (
	id int8 NOT NULL, -- 主键
	route_name varchar(30) NULL,
	route_id varchar(30) NULL,
	predicates text NULL, -- 断言
	filters text NULL, -- 过滤器
	uri varchar(50) NULL,
	sort_order int4 DEFAULT 0 NULL, -- 排序
	metadata text NULL, -- 路由元信息
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL,
	CONSTRAINT sys_route_conf_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_route_conf IS '路由配置表';

-- Column comments

COMMENT ON COLUMN public.sys_route_conf.id IS '主键';
COMMENT ON COLUMN public.sys_route_conf.predicates IS '断言';
COMMENT ON COLUMN public.sys_route_conf.filters IS '过滤器';
COMMENT ON COLUMN public.sys_route_conf.sort_order IS '排序';
COMMENT ON COLUMN public.sys_route_conf.metadata IS '路由元信息';
COMMENT ON COLUMN public.sys_route_conf.create_by IS '创建人';
COMMENT ON COLUMN public.sys_route_conf.update_by IS '修改人';
COMMENT ON COLUMN public.sys_route_conf.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_route_conf.update_time IS '修改时间';


-- public.sys_schedule definition

-- Drop table

-- DROP TABLE public.sys_schedule;

CREATE TABLE public.sys_schedule (
	id int8 NOT NULL, -- id
	title varchar(255) NULL, -- 标题
	schedule_type varchar(255) NULL, -- 日程类型
	schedule_state varchar(255) NULL, -- 状态
	"content" text NULL, -- 内容
	schedule_time time NULL, -- 时间
	schedule_date date NULL, -- 日期
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_schedule_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_schedule IS '系统日程管理表';

-- Column comments

COMMENT ON COLUMN public.sys_schedule.id IS 'id';
COMMENT ON COLUMN public.sys_schedule.title IS '标题';
COMMENT ON COLUMN public.sys_schedule.schedule_type IS '日程类型';
COMMENT ON COLUMN public.sys_schedule.schedule_state IS '状态';
COMMENT ON COLUMN public.sys_schedule."content" IS '内容';
COMMENT ON COLUMN public.sys_schedule.schedule_time IS '时间';
COMMENT ON COLUMN public.sys_schedule.schedule_date IS '日期';
COMMENT ON COLUMN public.sys_schedule.create_by IS '创建人';
COMMENT ON COLUMN public.sys_schedule.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_schedule.update_by IS '修改人';
COMMENT ON COLUMN public.sys_schedule.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_schedule.del_flag IS '删除标记';
COMMENT ON COLUMN public.sys_schedule.tenant_id IS '租户ID';


-- public.sys_sensitive_word definition

-- Drop table

-- DROP TABLE public.sys_sensitive_word;

CREATE TABLE public.sys_sensitive_word (
	sensitive_id int8 NOT NULL, -- 主键
	sensitive_word varchar(255) NULL, -- 敏感词
	sensitive_type varchar(1) NULL, -- 类型
	remark varchar(255) NULL, -- 备注
	create_by varchar(64) NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	update_by varchar(64) NULL, -- 修改人
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_sensitive_word_pkey PRIMARY KEY (sensitive_id)
);
COMMENT ON TABLE public.sys_sensitive_word IS '敏感词';

-- Column comments

COMMENT ON COLUMN public.sys_sensitive_word.sensitive_id IS '主键';
COMMENT ON COLUMN public.sys_sensitive_word.sensitive_word IS '敏感词';
COMMENT ON COLUMN public.sys_sensitive_word.sensitive_type IS '类型';
COMMENT ON COLUMN public.sys_sensitive_word.remark IS '备注';
COMMENT ON COLUMN public.sys_sensitive_word.create_by IS '创建人';
COMMENT ON COLUMN public.sys_sensitive_word.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_sensitive_word.update_by IS '修改人';
COMMENT ON COLUMN public.sys_sensitive_word.update_time IS '修改时间';
COMMENT ON COLUMN public.sys_sensitive_word.del_flag IS '删除标记';
COMMENT ON COLUMN public.sys_sensitive_word.tenant_id IS '租户ID';


-- public.sys_social_details definition

-- Drop table

-- DROP TABLE public.sys_social_details;

CREATE TABLE public.sys_social_details (
	id int8 NOT NULL, -- 主键
	"type" varchar(16) NULL, -- 社交登录类型
	remark varchar(64) NULL, -- 备注
	app_id varchar(64) NULL, -- 应用ID
	app_secret varchar(64) NULL, -- 应用密钥
	redirect_url varchar(128) NULL, -- 回调地址
	ext varchar(255) NULL, -- 拓展字段
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户
	CONSTRAINT sys_social_details_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_social_details IS '系统社交登录账号表';

-- Column comments

COMMENT ON COLUMN public.sys_social_details.id IS '主键';
COMMENT ON COLUMN public.sys_social_details."type" IS '社交登录类型';
COMMENT ON COLUMN public.sys_social_details.remark IS '备注';
COMMENT ON COLUMN public.sys_social_details.app_id IS '应用ID';
COMMENT ON COLUMN public.sys_social_details.app_secret IS '应用密钥';
COMMENT ON COLUMN public.sys_social_details.redirect_url IS '回调地址';
COMMENT ON COLUMN public.sys_social_details.ext IS '拓展字段';
COMMENT ON COLUMN public.sys_social_details.create_by IS '创建人';
COMMENT ON COLUMN public.sys_social_details.update_by IS '修改人';
COMMENT ON COLUMN public.sys_social_details.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_social_details.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_social_details.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.sys_social_details.tenant_id IS '所属租户';


-- public.sys_system_config definition

-- Drop table

-- DROP TABLE public.sys_system_config;

CREATE TABLE public.sys_system_config (
	id int8 NOT NULL, -- 主键
	config_type varchar(64) NULL, -- 配置类型
	config_name varchar(255) NULL, -- 配置名称
	config_key varchar(255) NULL, -- 配置标识
	config_value text NULL, -- 配置值
	config_status varchar(1) NULL, -- 开启状态
	create_by varchar(64) NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	update_by varchar(64) NULL, -- 修改人
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT sys_system_config_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_system_config IS '系统配置';

-- Column comments

COMMENT ON COLUMN public.sys_system_config.id IS '主键';
COMMENT ON COLUMN public.sys_system_config.config_type IS '配置类型';
COMMENT ON COLUMN public.sys_system_config.config_name IS '配置名称';
COMMENT ON COLUMN public.sys_system_config.config_key IS '配置标识';
COMMENT ON COLUMN public.sys_system_config.config_value IS '配置值';
COMMENT ON COLUMN public.sys_system_config.config_status IS '开启状态';
COMMENT ON COLUMN public.sys_system_config.create_by IS '创建人';
COMMENT ON COLUMN public.sys_system_config.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_system_config.update_by IS '修改人';
COMMENT ON COLUMN public.sys_system_config.update_time IS '修改时间';
COMMENT ON COLUMN public.sys_system_config.del_flag IS '删除标记';
COMMENT ON COLUMN public.sys_system_config.tenant_id IS '租户ID';


-- public.sys_tenant definition

-- Drop table

-- DROP TABLE public.sys_tenant;

CREATE TABLE public.sys_tenant (
	id int8 NOT NULL, -- 租户ID
	"name" varchar(255) NULL, -- 租户名称
	code varchar(64) NULL, -- 租户编码
	tenant_domain varchar(255) NULL, -- 租户域名
	website_name varchar(255) NULL, -- 网站名称
	mini_qr varchar(255) NULL, -- 移动端二维码
	background varchar(255) NULL, -- 登录页背景图
	footer varchar(255) NULL, -- 页脚信息
	logo varchar(255) NULL, -- logo
	start_time timestamp NULL, -- 租户开始时间
	end_time timestamp NULL, -- 租户结束时间
	status varchar(1) DEFAULT '0'::character varying NULL, -- 租户状态，0正常，1停用
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	menu_id text NULL, -- 租户菜单ID
	CONSTRAINT sys_tenant_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_tenant IS '租户表';

-- Column comments

COMMENT ON COLUMN public.sys_tenant.id IS '租户ID';
COMMENT ON COLUMN public.sys_tenant."name" IS '租户名称';
COMMENT ON COLUMN public.sys_tenant.code IS '租户编码';
COMMENT ON COLUMN public.sys_tenant.tenant_domain IS '租户域名';
COMMENT ON COLUMN public.sys_tenant.website_name IS '网站名称';
COMMENT ON COLUMN public.sys_tenant.mini_qr IS '移动端二维码';
COMMENT ON COLUMN public.sys_tenant.background IS '登录页背景图';
COMMENT ON COLUMN public.sys_tenant.footer IS '页脚信息';
COMMENT ON COLUMN public.sys_tenant.logo IS 'logo';
COMMENT ON COLUMN public.sys_tenant.start_time IS '租户开始时间';
COMMENT ON COLUMN public.sys_tenant.end_time IS '租户结束时间';
COMMENT ON COLUMN public.sys_tenant.status IS '租户状态，0正常，1停用';
COMMENT ON COLUMN public.sys_tenant.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.sys_tenant.create_by IS '创建人';
COMMENT ON COLUMN public.sys_tenant.update_by IS '修改人';
COMMENT ON COLUMN public.sys_tenant.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_tenant.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_tenant.menu_id IS '租户菜单ID';


-- public.sys_user definition

-- Drop table

-- DROP TABLE public.sys_user;

CREATE TABLE public.sys_user (
	user_id int8 NOT NULL, -- 用户ID
	username varchar(64) NULL, -- 用户名
	"password" varchar(255) NULL, -- 密码
	salt varchar(255) NULL, -- 盐值
	phone varchar(20) NULL, -- 电话号码
	avatar varchar(255) NULL, -- 头像
	nickname varchar(64) NULL, -- 昵称
	"name" varchar(64) NULL, -- 姓名
	email varchar(128) NULL, -- 邮箱地址
	dept_id int8 NULL, -- 所属部门ID
	create_by varchar(64) DEFAULT ' '::character varying NULL, -- 创建人
	update_by varchar(64) DEFAULT ' '::character varying NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 修改时间
	lock_flag varchar(1) DEFAULT '0'::character varying NULL, -- 锁定标记，0未锁定，9已锁定
	password_expire_flag varchar(1) DEFAULT '0'::character varying NULL, -- 密码是否过期，0未过期，9已过期
	password_modify_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	wx_openid varchar(32) NULL, -- 微信登录openId
	mini_openid varchar(32) NULL, -- 小程序openId
	qq_openid varchar(32) NULL, -- QQ openId
	gitee_login varchar(100) NULL, -- 码云标识
	osc_id varchar(100) NULL, -- 开源中国标识
	wx_cp_userid varchar(100) NULL, -- 企业微信唯一ID
	wx_ding_userid varchar(100) NULL, -- 钉钉唯一ID
	tenant_id int8 DEFAULT 0 NULL, -- 所属租户ID
	CONSTRAINT sys_user_pkey PRIMARY KEY (user_id)
);
COMMENT ON TABLE public.sys_user IS '用户表';

-- Column comments

COMMENT ON COLUMN public.sys_user.user_id IS '用户ID';
COMMENT ON COLUMN public.sys_user.username IS '用户名';
COMMENT ON COLUMN public.sys_user."password" IS '密码';
COMMENT ON COLUMN public.sys_user.salt IS '盐值';
COMMENT ON COLUMN public.sys_user.phone IS '电话号码';
COMMENT ON COLUMN public.sys_user.avatar IS '头像';
COMMENT ON COLUMN public.sys_user.nickname IS '昵称';
COMMENT ON COLUMN public.sys_user."name" IS '姓名';
COMMENT ON COLUMN public.sys_user.email IS '邮箱地址';
COMMENT ON COLUMN public.sys_user.dept_id IS '所属部门ID';
COMMENT ON COLUMN public.sys_user.create_by IS '创建人';
COMMENT ON COLUMN public.sys_user.update_by IS '修改人';
COMMENT ON COLUMN public.sys_user.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_user.update_time IS '修改时间';
COMMENT ON COLUMN public.sys_user.lock_flag IS '锁定标记，0未锁定，9已锁定';
COMMENT ON COLUMN public.sys_user.password_expire_flag IS '密码是否过期，0未过期，9已过期';
COMMENT ON COLUMN public.sys_user.password_modify_time IS '修改时间';
COMMENT ON COLUMN public.sys_user.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.sys_user.wx_openid IS '微信登录openId';
COMMENT ON COLUMN public.sys_user.mini_openid IS '小程序openId';
COMMENT ON COLUMN public.sys_user.qq_openid IS 'QQ openId';
COMMENT ON COLUMN public.sys_user.gitee_login IS '码云标识';
COMMENT ON COLUMN public.sys_user.osc_id IS '开源中国标识';
COMMENT ON COLUMN public.sys_user.wx_cp_userid IS '企业微信唯一ID';
COMMENT ON COLUMN public.sys_user.wx_ding_userid IS '钉钉唯一ID';
COMMENT ON COLUMN public.sys_user.tenant_id IS '所属租户ID';


-- public.sys_user_post definition

-- Drop table

-- DROP TABLE public.sys_user_post;

CREATE TABLE public.sys_user_post (
	user_id int8 NOT NULL, -- 用户ID
	post_id int8 NOT NULL, -- 岗位ID
	CONSTRAINT sys_user_post_pkey PRIMARY KEY (user_id, post_id)
);
COMMENT ON TABLE public.sys_user_post IS '用户与岗位关联表';

-- Column comments

COMMENT ON COLUMN public.sys_user_post.user_id IS '用户ID';
COMMENT ON COLUMN public.sys_user_post.post_id IS '岗位ID';


-- public.sys_user_role definition

-- Drop table

-- DROP TABLE public.sys_user_role;

CREATE TABLE public.sys_user_role (
	user_id int8 NOT NULL, -- 用户ID
	role_id int8 NOT NULL, -- 角色ID
	CONSTRAINT sys_user_role_pkey PRIMARY KEY (user_id, role_id)
);
COMMENT ON TABLE public.sys_user_role IS '用户角色表';

-- Column comments

COMMENT ON COLUMN public.sys_user_role.user_id IS '用户ID';
COMMENT ON COLUMN public.sys_user_role.role_id IS '角色ID';


-- public.wx_account definition

-- Drop table

-- DROP TABLE public.wx_account;

CREATE TABLE public.wx_account (
	id int8 NOT NULL, -- 主键ID
	"name" varchar(100) NULL, -- 名称
	account varchar(100) NULL, -- 账号
	appid varchar(100) NULL, -- 应用ID
	appsecret varchar(100) NULL, -- 应用秘钥
	url varchar(100) NULL, -- URL地址
	"token" varchar(100) NULL, -- Token令牌
	aeskey varchar(300) NULL, -- 消息加解密密钥
	qr_url varchar(200) NULL, -- 二维码URL地址
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT wx_account_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.wx_account IS '公众号账户表';

-- Column comments

COMMENT ON COLUMN public.wx_account.id IS '主键ID';
COMMENT ON COLUMN public.wx_account."name" IS '名称';
COMMENT ON COLUMN public.wx_account.account IS '账号';
COMMENT ON COLUMN public.wx_account.appid IS '应用ID';
COMMENT ON COLUMN public.wx_account.appsecret IS '应用秘钥';
COMMENT ON COLUMN public.wx_account.url IS 'URL地址';
COMMENT ON COLUMN public.wx_account."token" IS 'Token令牌';
COMMENT ON COLUMN public.wx_account.aeskey IS '消息加解密密钥';
COMMENT ON COLUMN public.wx_account.qr_url IS '二维码URL地址';
COMMENT ON COLUMN public.wx_account.create_time IS '创建时间';
COMMENT ON COLUMN public.wx_account.update_time IS '更新时间';
COMMENT ON COLUMN public.wx_account.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.wx_account.tenant_id IS '租户ID';


-- public.wx_account_fans definition

-- Drop table

-- DROP TABLE public.wx_account_fans;

CREATE TABLE public.wx_account_fans (
	id int8 NOT NULL, -- 主键ID
	openid varchar(100) NULL, -- 粉丝openid
	subscribe_status varchar(1) DEFAULT '0'::character varying NULL, -- 订阅状态，0未订阅，1已订阅
	subscribe_time timestamp NULL, -- 订阅时间
	nickname varchar(255) NULL, -- 昵称
	gender varchar(10) NULL, -- 性别
	"language" varchar(30) NULL, -- 语言
	country varchar(30) NULL, -- 国家
	province varchar(30) NULL, -- 省份
	city varchar(30) NULL, -- 城市
	tag_ids varchar(255) NULL, -- 分组ID
	headimg_url varchar(500) NULL, -- 头像URL地址
	remark varchar(500) NULL, -- 备注信息
	wx_account_id int8 NULL, -- 微信公众号ID
	wx_account_name varchar(32) NULL, -- 微信公众号名称
	wx_account_appid varchar(100) NULL, -- 微信公众号AppID
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	tenant_id int8 NULL, -- 租户ID
	is_black int4 NULL, -- 是否在黑名单
	CONSTRAINT wx_account_fans_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.wx_account_fans IS '微信公众号粉丝表';

-- Column comments

COMMENT ON COLUMN public.wx_account_fans.id IS '主键ID';
COMMENT ON COLUMN public.wx_account_fans.openid IS '粉丝openid';
COMMENT ON COLUMN public.wx_account_fans.subscribe_status IS '订阅状态，0未订阅，1已订阅';
COMMENT ON COLUMN public.wx_account_fans.subscribe_time IS '订阅时间';
COMMENT ON COLUMN public.wx_account_fans.nickname IS '昵称';
COMMENT ON COLUMN public.wx_account_fans.gender IS '性别';
COMMENT ON COLUMN public.wx_account_fans."language" IS '语言';
COMMENT ON COLUMN public.wx_account_fans.country IS '国家';
COMMENT ON COLUMN public.wx_account_fans.province IS '省份';
COMMENT ON COLUMN public.wx_account_fans.city IS '城市';
COMMENT ON COLUMN public.wx_account_fans.tag_ids IS '分组ID';
COMMENT ON COLUMN public.wx_account_fans.headimg_url IS '头像URL地址';
COMMENT ON COLUMN public.wx_account_fans.remark IS '备注信息';
COMMENT ON COLUMN public.wx_account_fans.wx_account_id IS '微信公众号ID';
COMMENT ON COLUMN public.wx_account_fans.wx_account_name IS '微信公众号名称';
COMMENT ON COLUMN public.wx_account_fans.wx_account_appid IS '微信公众号AppID';
COMMENT ON COLUMN public.wx_account_fans.create_time IS '创建时间';
COMMENT ON COLUMN public.wx_account_fans.update_time IS '更新时间';
COMMENT ON COLUMN public.wx_account_fans.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.wx_account_fans.tenant_id IS '租户ID';
COMMENT ON COLUMN public.wx_account_fans.is_black IS '是否在黑名单';


-- public.wx_account_tag definition

-- Drop table

-- DROP TABLE public.wx_account_tag;

CREATE TABLE public.wx_account_tag (
	id int8 NOT NULL, -- 主键ID
	tag varchar(100) NULL, -- 标签名称
	create_by varchar(64) NULL, -- 创建人
	update_by varchar(64) NULL, -- 修改人
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 修改时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记，0未删除，1已删除
	tenant_id int8 NULL, -- 租户ID
	wx_account_id int8 NULL, -- 微信公众号ID
	wx_account_name varchar(255) NULL, -- 微信公众号名称
	wx_account_appid varchar(255) NULL, -- 微信公众号AppID
	tag_id int8 NULL, -- 标签ID
	CONSTRAINT wx_account_tag_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.wx_account_tag IS '微信公众号标签表';

-- Column comments

COMMENT ON COLUMN public.wx_account_tag.id IS '主键ID';
COMMENT ON COLUMN public.wx_account_tag.tag IS '标签名称';
COMMENT ON COLUMN public.wx_account_tag.create_by IS '创建人';
COMMENT ON COLUMN public.wx_account_tag.update_by IS '修改人';
COMMENT ON COLUMN public.wx_account_tag.create_time IS '创建时间';
COMMENT ON COLUMN public.wx_account_tag.update_time IS '修改时间';
COMMENT ON COLUMN public.wx_account_tag.del_flag IS '删除标记，0未删除，1已删除';
COMMENT ON COLUMN public.wx_account_tag.tenant_id IS '租户ID';
COMMENT ON COLUMN public.wx_account_tag.wx_account_id IS '微信公众号ID';
COMMENT ON COLUMN public.wx_account_tag.wx_account_name IS '微信公众号名称';
COMMENT ON COLUMN public.wx_account_tag.wx_account_appid IS '微信公众号AppID';
COMMENT ON COLUMN public.wx_account_tag.tag_id IS '标签ID';


-- public.wx_auto_reply definition

-- Drop table

-- DROP TABLE public.wx_auto_reply;

CREATE TABLE public.wx_auto_reply (
	id int8 NOT NULL, -- 主键
	"type" varchar(2) NULL, -- 类型（1、关注时回复；2、消息回复；3、关键词回复）
	req_key varchar(64) NULL, -- 关键词
	req_type varchar(10) NULL, -- 请求消息类型（text：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置）
	rep_type varchar(10) NULL, -- 回复消息类型（text：文本；image：图片；voice：语音；video：视频；music：音乐；news：图文）
	rep_mate varchar(10) NULL, -- 回复类型文本匹配类型（1、全匹配，2、半匹配）
	rep_content text NULL, -- 回复类型文本保存文字
	rep_media_id varchar(64) NULL, -- 回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id
	rep_name varchar(100) NULL, -- 回复的素材名、视频和音乐的标题
	rep_desc varchar(200) NULL, -- 视频和音乐的描述
	rep_url varchar(500) NULL, -- 链接
	rep_hq_url varchar(500) NULL, -- 高质量链接
	rep_thumb_media_id varchar(64) NULL, -- 缩略图的媒体id
	rep_thumb_url varchar(500) NULL, -- 缩略图url
	"content" text NULL, -- 图文消息的内容
	app_id varchar(100) NULL, -- 公众号ID
	remark varchar(100) NULL, -- 备注
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 逻辑删除标记（0：显示；1：隐藏）
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT wx_auto_reply_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.wx_auto_reply IS '微信自动回复';

-- Column comments

COMMENT ON COLUMN public.wx_auto_reply.id IS '主键';
COMMENT ON COLUMN public.wx_auto_reply."type" IS '类型（1、关注时回复；2、消息回复；3、关键词回复）';
COMMENT ON COLUMN public.wx_auto_reply.req_key IS '关键词';
COMMENT ON COLUMN public.wx_auto_reply.req_type IS '请求消息类型（text：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置）';
COMMENT ON COLUMN public.wx_auto_reply.rep_type IS '回复消息类型（text：文本；image：图片；voice：语音；video：视频；music：音乐；news：图文）';
COMMENT ON COLUMN public.wx_auto_reply.rep_mate IS '回复类型文本匹配类型（1、全匹配，2、半匹配）';
COMMENT ON COLUMN public.wx_auto_reply.rep_content IS '回复类型文本保存文字';
COMMENT ON COLUMN public.wx_auto_reply.rep_media_id IS '回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id';
COMMENT ON COLUMN public.wx_auto_reply.rep_name IS '回复的素材名、视频和音乐的标题';
COMMENT ON COLUMN public.wx_auto_reply.rep_desc IS '视频和音乐的描述';
COMMENT ON COLUMN public.wx_auto_reply.rep_url IS '链接';
COMMENT ON COLUMN public.wx_auto_reply.rep_hq_url IS '高质量链接';
COMMENT ON COLUMN public.wx_auto_reply.rep_thumb_media_id IS '缩略图的媒体id';
COMMENT ON COLUMN public.wx_auto_reply.rep_thumb_url IS '缩略图url';
COMMENT ON COLUMN public.wx_auto_reply."content" IS '图文消息的内容';
COMMENT ON COLUMN public.wx_auto_reply.app_id IS '公众号ID';
COMMENT ON COLUMN public.wx_auto_reply.remark IS '备注';
COMMENT ON COLUMN public.wx_auto_reply.del_flag IS '逻辑删除标记（0：显示；1：隐藏）';
COMMENT ON COLUMN public.wx_auto_reply.create_time IS '创建时间';
COMMENT ON COLUMN public.wx_auto_reply.update_time IS '更新时间';
COMMENT ON COLUMN public.wx_auto_reply.tenant_id IS '租户ID';


-- public.wx_mp_menu definition

-- Drop table

-- DROP TABLE public.wx_mp_menu;

CREATE TABLE public.wx_mp_menu (
	id int8 NOT NULL, -- 主键
	menu text NULL, -- 菜单
	wx_account_id int8 NULL, -- 公众号ID
	wx_account_appid varchar(100) NULL, -- 公众号APPID
	wx_account_name varchar(32) NULL, -- 公众号名称
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 删除标记
	pub_flag varchar(1) DEFAULT '0'::character varying NULL, -- 发布标志
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT wx_mp_menu_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.wx_mp_menu IS '微信菜单表';

-- Column comments

COMMENT ON COLUMN public.wx_mp_menu.id IS '主键';
COMMENT ON COLUMN public.wx_mp_menu.menu IS '菜单';
COMMENT ON COLUMN public.wx_mp_menu.wx_account_id IS '公众号ID';
COMMENT ON COLUMN public.wx_mp_menu.wx_account_appid IS '公众号APPID';
COMMENT ON COLUMN public.wx_mp_menu.wx_account_name IS '公众号名称';
COMMENT ON COLUMN public.wx_mp_menu.create_time IS '创建时间';
COMMENT ON COLUMN public.wx_mp_menu.update_time IS '更新时间';
COMMENT ON COLUMN public.wx_mp_menu.del_flag IS '删除标记';
COMMENT ON COLUMN public.wx_mp_menu.pub_flag IS '发布标志';
COMMENT ON COLUMN public.wx_mp_menu.tenant_id IS '租户ID';


-- public.wx_msg definition

-- Drop table

-- DROP TABLE public.wx_msg;

CREATE TABLE public.wx_msg (
	id int8 NOT NULL, -- 主键
	app_name varchar(50) NULL, -- 公众号名称
	app_logo varchar(500) NULL, -- 公众号logo
	wx_user_id varchar(32) NULL, -- 微信用户ID
	nick_name varchar(200) NULL, -- 微信用户昵称
	headimg_url varchar(1000) NULL, -- 微信用户头像
	"type" varchar(2) NULL, -- 消息分类（1、用户发给公众号；2、公众号发给用户；）
	rep_type varchar(20) NULL, -- 消息类型（text：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置；music：音乐；news：图文；event：推送事件）
	rep_event varchar(20) NULL, -- 事件类型（subscribe：关注；unsubscribe：取关；CLICK、VIEW：菜单事件）
	rep_content text NULL, -- 回复类型文本保存文字、地理位置信息
	rep_media_id varchar(64) NULL, -- 回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id
	rep_name varchar(100) NULL, -- 回复的素材名、视频和音乐的标题
	rep_desc varchar(200) NULL, -- 视频和音乐的描述
	rep_url varchar(500) NULL, -- 链接
	rep_hq_url varchar(500) NULL, -- 高质量链接
	"content" text NULL, -- 图文消息的内容
	rep_thumb_media_id varchar(64) NULL, -- 缩略图的媒体id
	rep_thumb_url varchar(500) NULL, -- 缩略图url
	rep_location_x float8 NULL, -- 地理位置维度
	rep_location_y float8 NULL, -- 地理位置经度
	rep_scale float8 NULL, -- 地图缩放大小
	read_flag varchar(2) DEFAULT '1'::character varying NULL, -- 已读标记（1：是；0：否）
	app_id varchar(100) NULL, -- 公众号ID
	open_id varchar(100) NULL, -- 微信唯一标识
	remark varchar(100) NULL, -- 备注
	del_flag varchar(1) DEFAULT '0'::character varying NULL, -- 逻辑删除标记（0：显示；1：隐藏）
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	tenant_id int8 NULL, -- 租户ID
	CONSTRAINT wx_msg_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.wx_msg IS '微信消息';

-- Column comments

COMMENT ON COLUMN public.wx_msg.id IS '主键';
COMMENT ON COLUMN public.wx_msg.app_name IS '公众号名称';
COMMENT ON COLUMN public.wx_msg.app_logo IS '公众号logo';
COMMENT ON COLUMN public.wx_msg.wx_user_id IS '微信用户ID';
COMMENT ON COLUMN public.wx_msg.nick_name IS '微信用户昵称';
COMMENT ON COLUMN public.wx_msg.headimg_url IS '微信用户头像';
COMMENT ON COLUMN public.wx_msg."type" IS '消息分类（1、用户发给公众号；2、公众号发给用户；）';
COMMENT ON COLUMN public.wx_msg.rep_type IS '消息类型（text：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置；music：音乐；news：图文；event：推送事件）';
COMMENT ON COLUMN public.wx_msg.rep_event IS '事件类型（subscribe：关注；unsubscribe：取关；CLICK、VIEW：菜单事件）';
COMMENT ON COLUMN public.wx_msg.rep_content IS '回复类型文本保存文字、地理位置信息';
COMMENT ON COLUMN public.wx_msg.rep_media_id IS '回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id';
COMMENT ON COLUMN public.wx_msg.rep_name IS '回复的素材名、视频和音乐的标题';
COMMENT ON COLUMN public.wx_msg.rep_desc IS '视频和音乐的描述';
COMMENT ON COLUMN public.wx_msg.rep_url IS '链接';
COMMENT ON COLUMN public.wx_msg.rep_hq_url IS '高质量链接';
COMMENT ON COLUMN public.wx_msg."content" IS '图文消息的内容';
COMMENT ON COLUMN public.wx_msg.rep_thumb_media_id IS '缩略图的媒体id';
COMMENT ON COLUMN public.wx_msg.rep_thumb_url IS '缩略图url';
COMMENT ON COLUMN public.wx_msg.rep_location_x IS '地理位置维度';
COMMENT ON COLUMN public.wx_msg.rep_location_y IS '地理位置经度';
COMMENT ON COLUMN public.wx_msg.rep_scale IS '地图缩放大小';
COMMENT ON COLUMN public.wx_msg.read_flag IS '已读标记（1：是；0：否）';
COMMENT ON COLUMN public.wx_msg.app_id IS '公众号ID';
COMMENT ON COLUMN public.wx_msg.open_id IS '微信唯一标识';
COMMENT ON COLUMN public.wx_msg.remark IS '备注';
COMMENT ON COLUMN public.wx_msg.del_flag IS '逻辑删除标记（0：显示；1：隐藏）';
COMMENT ON COLUMN public.wx_msg.create_time IS '创建时间';
COMMENT ON COLUMN public.wx_msg.update_time IS '更新时间';
COMMENT ON COLUMN public.wx_msg.tenant_id IS '租户ID';




INSERT INTO public.app_page (id,page_type,page_name,page_data,create_by,update_by,create_time,update_time,del_flag,tenant_id) VALUES
	 (1,1,'商城首页','[{"title":"搜索","name":"search","disabled":1,"content":{},"styles":{}},{"title":"首页轮播图","name":"banner","content":{"enabled":1,"data":[{"image":"/api/static/banner01.png","name":"","link":{"path":"/pages/index/index","name":"商城首页","type":"shop"}}]},"styles":{}},{"title":"导航菜单","name":"nav","content":{"enabled":1,"data":[{"image":"https://minio.pigx.top/oss/app/nav01.png","name":"资讯中心","link":{"path":"/pages/news/news","name":"文章资讯","type":"shop"}},{"image":"https://minio.pigx.top/oss/app/nav02.png","name":"我的收藏","link":{"path":"/pages/collection/collection","name":"我的收藏","type":"shop"}},{"image":"https://minio.pigx.top/oss/app/nav03.png","name":"个人设置","link":{"path":"/pages/user_set/user_set","name":"个人设置","type":"shop"}},{"image":"https://minio.pigx.top/oss/app/nav04.png","name":"联系客服","link":{"path":"/pages/customer_service/customer_service","name":"联系客服","type":"shop"}},{"image":"https://minio.pigx.top/oss/app/nav05.png","name":"关于我们","link":{"path":"/pages/as_us/as_us","name":"关于我们","type":"shop"}}]},"styles":{}},{"id":"l84almsk2uhyf","title":"资讯","name":"news","disabled":1,"content":{},"styles":{}}]',NULL,'admin',NULL,'2023-06-15 09:18:02','0',1),
	 (2,2,'个人中心','[{"title":"用户信息","name":"user-info","disabled":1,"content":{},"styles":{}},{"title":"我的服务","name":"my-service","content":{"style":2,"title":"服务中心","data":[{"image":"https://minio.pigx.top/oss/app/user_collect.png","name":"我的收藏","link":{"path":"/pages/collection/collection","name":"我的收藏","type":"shop"}},{"image":"https://minio.pigx.top/oss/app/user_setting.png","name":"个人设置","link":{"path":"/pages/user_set/user_set","name":"个人设置","type":"shop"}},{"image":"https://minio.pigx.top/oss/app/user_kefu.png","name":"联系客服","link":{"path":"/pages/customer_service/customer_service","name":"联系客服","type":"shop"}}]},"styles":{}},{"title":"个人中心广告图","name":"user-banner","content":{"enabled":1,"data":[{"image":"","name":"sdds","link":{"path":"/pages/user/user","name":"个人中心","type":"shop"}}]},"styles":{}}]',NULL,'admin',NULL,'2023-06-18 17:00:05','0',1),
	 (3,3,'客服设置','[{"title":"客服设置","name":"customer-service","content":{"title":"添加客服二维码","time":"早上 9:00 - 22:00","mobile":"13800138000","qrcode":"/admin/sys-file/local/adc5061f99e9440abcd9b22572909c88.jpg"},"styles":{}}]',NULL,'admin',NULL,'2023-06-14 13:12:19','0',1);
INSERT INTO public.app_role (role_id,role_name,role_code,role_desc,create_by,update_by,create_time,update_time,del_flag,tenant_id) VALUES
	 (1,'app用户','APP_USER','app用户角色','','','2022-12-07 06:34:18','2023-03-09 06:34:42','0',1);
INSERT INTO public.app_social_details (id,"type",remark,app_id,app_secret,redirect_url,ext,create_by,update_by,create_time,update_time,del_flag) VALUES
	 (1,'MINI','小程序登录','app_id','app_secret','http://www.baidu.com123',NULL,'','admin','2022-12-09 01:44:42','2023-04-03 06:12:30','0');
INSERT INTO public.app_tabbar (id,"name",selected,unselected,link,create_time,update_time,create_by,update_by,del_flag,tenant_id) VALUES
	 (1,'首页','https://minio.pigx.top/oss/app/tabbar0.png','https://minio.pigx.top/oss/app/tabbar0_0.png','{"path":"/pages/index/index","name":"商城首页","type":"shop"}',NULL,'2023-06-15 09:16:25',NULL,'admin','0',1),
	 (2,'资讯','https://minio.pigx.top/oss/app/tabbar1.png','https://minio.pigx.top/oss/app/tabbar1_1.png','{"path":"/pages/news/news","name":"文章资讯","type":"shop"}',NULL,'2023-06-15 09:16:25',NULL,'admin','0',1),
	 (3,'我的','https://minio.pigx.top/oss/app/tabbar3.png','https://minio.pigx.top/oss/app/tabbar3_3.png','{"path":"/pages/user/user","name":"个人中心","type":"shop"}',NULL,'2023-06-15 09:16:25',NULL,'admin','0',1);
INSERT INTO public.app_user (user_id,username,"password",salt,phone,avatar,nickname,"name",email,create_by,update_by,create_time,update_time,del_flag,tenant_id,last_modified_time,lock_flag,wx_openid) VALUES
	 (1,'appuser','$2a$10$XQu3TmORLqDWayFspQN.U.LigJ5TWPTdXPIn/6SxGHKED3PVpuMH6',NULL,'13054729089',NULL,'aeizzz','刘洪磊','<EMAIL>','','appuser','2022-12-07 02:59:38','2023-03-09 15:14:44','0',1,NULL,'0','oBxPy5EnbDiN-gGEaovCpp_IkrkQ');
INSERT INTO public.app_user_role (user_id,role_id) VALUES
	 (1,1);
INSERT INTO public.gen_field_type (id,column_type,attr_type,package_name,create_time,create_by,update_time,update_by,del_flag) VALUES
	 (1,'datetime','LocalDateTime','java.time.LocalDateTime','2023-02-06 08:45:10',NULL,NULL,NULL,'0'),
	 (2,'date','LocalDate','java.time.LocalDate','2023-02-06 08:45:10',NULL,NULL,NULL,'0'),
	 (3,'tinyint','Integer',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (4,'smallint','Integer',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (5,'mediumint','Integer',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (6,'int','Integer',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (7,'integer','Integer',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (8,'bigint','Long',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (9,'float','Float',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (10,'double','Double',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0');
INSERT INTO public.gen_field_type (id,column_type,attr_type,package_name,create_time,create_by,update_time,update_by,del_flag) VALUES
	 (11,'decimal','BigDecimal','java.math.BigDecimal','2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (12,'bit','Boolean',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (13,'char','String',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (14,'varchar','String',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (15,'tinytext','String',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (16,'text','String',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (17,'mediumtext','String',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (18,'longtext','String',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (19,'timestamp','LocalDateTime','java.time.LocalDateTime','2023-02-06 08:45:11',NULL,NULL,NULL,'0'),
	 (20,'NUMBER','Integer',NULL,'2023-02-06 08:45:11',NULL,NULL,NULL,'0');
INSERT INTO public.gen_field_type (id,column_type,attr_type,package_name,create_time,create_by,update_time,update_by,del_flag) VALUES
	 (21,'BINARY_INTEGER','Integer',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (22,'BINARY_FLOAT','Float',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (23,'BINARY_DOUBLE','Double',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (24,'VARCHAR2','String',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (25,'NVARCHAR','String',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (26,'NVARCHAR2','String',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (27,'CLOB','String',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (28,'int8','Long',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (29,'int4','Integer',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (30,'int2','Integer',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0');
INSERT INTO public.gen_field_type (id,column_type,attr_type,package_name,create_time,create_by,update_time,update_by,del_flag) VALUES
	 (31,'numeric','BigDecimal','java.math.BigDecimal','2023-02-06 08:45:12',NULL,NULL,NULL,'0'),
	 (32,'json','String',NULL,'2023-02-06 08:45:12',NULL,NULL,NULL,'0');
INSERT INTO public.gen_group (id,group_name,group_desc,tenant_id,create_by,update_by,create_time,update_time,del_flag) VALUES
	 (1,'单表增删改查','单表增删改查',1,' ',' ',NULL,NULL,'0'),
	 (2,'主子表表增删改查','主子表表增删改查',1,' ',' ',NULL,NULL,'0');
INSERT INTO public.gen_template (id,template_name,generator_path,template_desc,template_code,create_time,update_time,del_flag,tenant_id,create_by,update_by) VALUES
	 (1,'vform.json','/','表单设计器初始化json模板','#set($key=${dateTool.getSystemTime()})
{
  "widgetList": [
    {
      "key": $key,
      "type": "grid",
      "category": "container",
      "icon": "grid",
      "cols": [
#foreach($field in $formList)
#if($field.attrName != ${pk.attrName})
        {
          "type": "grid-col",
          "category": "container",
          "icon": "grid-col",
          "internal": true,
          "widgetList": [
            {
              "key": ${math.add($key,${foreach.index})},
	#if($field.formType == ''text'')
              "type": "input",
              "icon": "text-field",
	#elseif($field.formType == ''number'')
              "type": "number",
              "icon": "number-field",
	#elseif($field.formType == ''textarea'')
              "type": "textarea",
              "icon": "textarea-field",
	#elseif($field.formType == ''select'' && ${field.fieldDict})
              "type": "select",
              "icon": "select-field",
	#elseif($field.formType == ''radio'' && ${field.fieldDict})
              "type": "radio",
              "icon": "radio-field",
	#elseif($field.formType == ''checkbox''  && ${field.fieldDict} )
              "type": "checkbox",
              "icon": "checkbox-field",
	#elseif($field.formType == ''date'')
              "type": "date",
              "icon": "date-field",
	#elseif($field.formType == ''datetime'')
              "type": "time",
              "icon": "time-field",
	#elseif($field.formType == ''upload-file'')
              "type": "file-upload",
              "icon": "file-upload-field",
	#elseif($field.formType == ''upload-img'')
              "type": "picture-upload",
              "icon": "picture-upload-field",
	#elseif($field.formType == ''editor'')
              "type": "rich-editor",
              "icon": "rich-editor-field",
	#else
              "type": "input",
              "icon": "text-field",
	#end
              "formItemFlag": true,
              "options": {
	                "name": "${field.attrName}",
	                "label": "#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end",
	#if(($field.formType == ''select'' || $field.formType == ''radio'' || $field.formType == ''checkbox'') && ${field.fieldDict})
                    "optionItemsDictType": "${field.fieldDict}",
	#end
                    "placeholder": "请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"
              },
    #if($field.formRequired)
             "required": true,
    #end
              "id": "input${math.add($key,${foreach.index})}"
            }
          ],
          "options": {
            "name": "gridCol${math.add($key,${foreach.index})}",
            "hidden": false,
            "offset": 0,
            "push": 0,
            "pull": 0,
	#if($formLayout == 1)
            "span": 24,
	#elseif($formLayout == 2)
            "span": 12,
	#end
            "responsive": false
          },
          "id": "grid-col-${math.add($key,${foreach.index})}"
        }#if($foreach.hasNext),#end
#end
#end
      ],
      "options": {
        "name": "grid${functionName}",
        "hidden": false,
        "gutter": 12
      },
      "id": "grid${functionName}"
    }
  ],
  "formConfig": {
    "modelName": "form",
    "refName": "form",
    "rulesName": "rules",
    "labelWidth": 80,
    "labelPosition": "left",
    "labelAlign": "label-left-align",
    "layoutType": "PC",
    "jsonVersion": 3
  }
}','2023-02-23 04:33:16','2023-06-04 10:35:51','0',1,' ',' '),
	 (2,'vform.vue','/','表单设计器生成sfc模板','<template>
    <el-dialog :title="form.${pk.attrName} ? ''编辑'' : ''新增''" v-model="visible" :close-on-click-modal="false" draggable>
      <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px">
#foreach($key in $resultMap.keySet())
#set($itemList = $resultMap.get($key))
<el-row :gutter="24">
#foreach($field in $itemList)
  <el-col :span="$field.span">
#if($field.type == ''input'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
          <el-input v-model="form.${field.options.name}" placeholder="${field.options.placeholder}"/>
        </el-form-item>
#elseif($field.type == ''number'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
          <el-input-number :min="${field.options.min}" :max="${field.options.max}" v-model="form.${field.options.name}" placeholder="${field.options.placeholder}"></el-input-number>
        </el-form-item>
#elseif($field.type == ''textarea'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
          <el-input type="textarea" :rows="${field.options.rows}" v-model="form.${field.options.name}" placeholder="${field.options.placeholder}"/>
        </el-form-item>
#elseif($field.type == ''select'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <el-select v-model="form.${field.options.name}" placeholder="${field.options.placeholder}">
       #if($field.options.optionItemsDictType)
                <el-option :value="item.value" :label="item.label" v-for="(item, index) in ${field.options.optionItemsDictType}" :key="index"></el-option>
       #else
                <el-option label="请选择">0</el-option>
       #end
            </el-select>
        </el-form-item>
#elseif($field.type == ''radio'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <el-radio-group v-model="form.${field.options.name}">
       #if($field.options.optionItemsDictType)
             <el-radio :label="item.value" v-for="(item, index) in ${field.options.optionItemsDictType}" border :key="index">{{ item.label }}
              </el-radio>
       #end
            </el-radio-group>
        </el-form-item>
#elseif($field.type == ''checkbox'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <el-checkbox-group v-model="form.${field.options.name}">
       #if($field.options.optionItemsDictType)
                <el-checkbox :label="item.value" :name="item.label" v-for="(item, index) in ${field.options.optionItemsDictType}" :key="index"></el-checkbox>
       #else
                <el-checkbox label="启用" name="type"></el-checkbox>
                <el-checkbox label="禁用" name="type"></el-checkbox>
       #end
            </<el-checkbox-group>
        </el-form-item>
#elseif($field.type == ''date'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <el-date-picker type="date" placeholder="${field.options.placeholder}" v-model="form.${field.options.name}" :value-format="dateStr"></el-date-picker>
        </el-form-item>
#elseif($field.type == ''time'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <el-time-picker placeholder="${field.options.placeholder}" v-model="form.${field.options.name}" :value-format="dateTimeStr"></el-date-picker>
        </el-form-item>
#elseif($field.type == ''file-upload'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <upload-file  v-model="form.${field.attrName}" limit="${field.options.limit}" fileMaxSize="${field.options.fileMaxSize}"></upload-file>
        </el-form-item>
#elseif($field.type == ''picture-upload'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <upload-img v-model:imageUrl="form.${field.options.name}" limit="${field.options.limit}" fileMaxSize="${field.options.fileMaxSize}"></upload-img>
        </el-form-item>
#elseif($field.type == ''rich-editor'')
          <el-form-item label="${field.options.label}" prop="${field.options.name}">
            <editor v-model:get-html="form.${field.options.name}" placeholder="${field.options.placeholder}"></editor>
          </el-form-item>
#elseif($field.type == ''switch'')
          <el-form-item label="${field.options.label}" prop="${field.options.name}">
          <el-switch v-model="form.${field.options.name}" />
          </el-form-item>
#elseif($field.type == ''rate'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
          <el-rate v-model="form.${field.options.name}" />
      </el-form-item>
#elseif($field.type == ''slider'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
          <el-slider v-model="form.${field.options.name}" />
      </el-form-item>
#elseif($field.type == ''color'')
        <el-form-item label="${field.options.label}" prop="${field.options.name}">
          <el-color-picker v-model="form.${field.options.name}" />
      </el-form-item>
#elseif($field.type == ''static-text'' || $field.type == ''html-text'')
        <span>{{form.${field.options.name}}}</span>          
#elseif($field.type == ''divider'')
      <el-divider />
#else
      <el-form-item label="${field.options.label}" prop="${field.options.name}">
        <el-input v-model="form.${field.options.name}" placeholder="${field.options.placeholder}"/>
      </el-form-item>
#end
  </el-col>
#end
</el-row>
#end
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="visible = false" formDialogRef>取消</el-button>
          <el-button type="primary" @click="onSubmit" formDialogRef>确认</el-button>
        </span>
      </template>
    </el-dialog>
</template>

<script setup lang="ts" name="${ClassName}Dialog">
import { useDict } from ''/@/hooks/dict'';
import { useMessage } from "/@/hooks/message";
import { getObj, addObj, putObj } from ''/@/api/${moduleName}/${functionName}''
import { rule } from ''/@/utils/validate'';
const emit = defineEmits([''refresh'']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false)
// 定义字典
#set($fieldDict=[])
#foreach($key in $resultMap.keySet())
#set($itemList = $resultMap.get($key))
#foreach($field in $itemList)
   #if($field.options.optionItemsDictType)
        #set($void=$fieldDict.add($field.options.optionItemsDictType))
    #end
#end
#end
#if($fieldDict)
const { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))
#end

// 提交表单数据
const form = reactive({
		${pk.attrName}:"",
#foreach($key in $resultMap.keySet())
#set($itemList = $resultMap.get($key))
#foreach($field in $itemList)
    ${field.options.name}: "",
#end
#end
});

// 定义校验规则
const dataRules = ref({
#foreach($key in $resultMap.keySet())
#set($itemList = $resultMap.get($key))
#foreach($field in $itemList)
#if($field.options.required && $field.options.validation)
    ${field.options.name}: [{required: true, message: ''${field.options.label}不能为空'', trigger: ''blur''}, {{ validator: rule.${field.options.validation}, trigger: ''blur'' }],
#elseif($field.options.required)
    ${field.options.name}: [{required: true, message: ''${field.options.label}不能为空'', trigger: ''blur''}],
#elseif($field.options.validation)
   ${field.options.name}: [{ validator: rule.${field.options.validation}, trigger: ''blur'' }],
#end
#end
#end
})

// 打开弹窗
const openDialog = (id: string) => {
  visible.value = true
  form.${pk.attrName} = ''''

  // 重置表单数据
    nextTick(() => {
        dataFormRef.value?.resetFields();
    });
  
  // 获取${className}信息
  if (id) {
    form.${pk.attrName} = id
    get${className}Data(id)
  }
};

// 提交
const onSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false
    }

    // 更新
    if (form.${pk.attrName}) {
      putObj(form).then(() => {
        useMessage().success(''修改成功'')
        visible.value = false // 关闭弹窗
        emit(''refresh'')
      }).catch((err: any) => {
        useMessage().error(err.msg)
      })
    } else {
      addObj(form).then(() => {
        useMessage().success(''添加成功'')
        visible.value = false // 关闭弹窗
        emit(''refresh'')
      }).catch((err: any) => {
        useMessage().error(err.msg)
      })
    }
  })
}

// 初始化表单数据
const get${className}Data = (id: string) => {
  // 获取数据
  getObj(id).then((res: any) => {
    Object.assign(form, res.data)
  })
};

// 暴露变量
defineExpose({
  openDialog
});
</script>','2023-02-23 04:33:52','2023-08-28 22:08:59','0',1,'','admin'),
	 (3,'Controller','${backendPath}/src/main/java/${packagePath}/${moduleName}/controller/${ClassName}Controller.java','后台Controller','package ${package}.${moduleName}.controller;

#if($queryList)
import cn.hutool.core.util.StrUtil;
#end
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import ${package}.${moduleName}.entity.${ClassName}Entity;
import ${package}.${moduleName}.service.${ClassName}Service;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
#if($isSpringBoot3)
import org.springdoc.core.annotations.ParameterObject;
#else
import org.springdoc.api.annotations.ParameterObject;
#end
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * ${tableComment}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/${functionName}" )
@Tag(description = "${functionName}" , name = "${tableComment}管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ${ClassName}Controller {

    private final  ${ClassName}Service ${className}Service;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ${className} ${tableComment}
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_view'')" )
    public R get${ClassName}Page(@ParameterObject Page page, @ParameterObject ${ClassName}Entity ${className}) {
        LambdaQueryWrapper<${ClassName}Entity> wrapper = Wrappers.lambdaQuery();
#foreach ($field in $queryList)
#set($getAttrName=$str.getProperty($field.attrName))
#set($var="${className}.$getAttrName()")
#if($field.attrType == ''String'')
#set($expression="StrUtil.isNotBlank")
#else
#set($expression="Objects.nonNull")
#end
#if($field.queryType == ''='')
		wrapper.eq($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''like'' )
		wrapper.like($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''!-'' )
		wrapper.ne($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''>'' )
		wrapper.gt($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''<'' )
		wrapper.lt($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''>='' )
		wrapper.ge($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''<='' )
		wrapper.le($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''left like'' )
		wrapper.likeLeft($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''right like'' )
		wrapper.likeRight($expression($var),${ClassName}Entity::$getAttrName,$var);
#end
#end
        return R.ok(${className}Service.page(page, wrapper));
    }


    /**
     * 通过id查询${tableComment}
     * @param ${pk.attrName} id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{${pk.attrName}}" )
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_view'')" )
    public R getById(@PathVariable("${pk.attrName}" ) ${pk.attrType} ${pk.attrName}) {
        return R.ok(${className}Service.getById(${pk.attrName}));
    }

    /**
     * 新增${tableComment}
     * @param ${className} ${tableComment}
     * @return R
     */
    @Operation(summary = "新增${tableComment}" , description = "新增${tableComment}" )
    @SysLog("新增${tableComment}" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_add'')" )
    public R save(@RequestBody ${ClassName}Entity ${className}) {
        return R.ok(${className}Service.save(${className}));
    }

    /**
     * 修改${tableComment}
     * @param ${className} ${tableComment}
     * @return R
     */
    @Operation(summary = "修改${tableComment}" , description = "修改${tableComment}" )
    @SysLog("修改${tableComment}" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_edit'')" )
    public R updateById(@RequestBody ${ClassName}Entity ${className}) {
        return R.ok(${className}Service.updateById(${className}));
    }

    /**
     * 通过id删除${tableComment}
     * @param ids ${pk.attrName}列表
     * @return R
     */
    @Operation(summary = "通过id删除${tableComment}" , description = "通过id删除${tableComment}" )
    @SysLog("通过id删除${tableComment}" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_del'')" )
    public R removeById(@RequestBody ${pk.attrType}[] ids) {
        return R.ok(${className}Service.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param ${className} 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_export'')" )
    public List<${ClassName}Entity> export(${ClassName}Entity ${className},${pk.attrType}[] ids) {
        return ${className}Service.list(Wrappers.lambdaQuery(${className}).in(ArrayUtil.isNotEmpty(ids), ${ClassName}Entity::$str.getProperty($pk.attrName), ids));
    }
}','2023-02-23 01:16:17','2023-10-29 12:18:12','0',1,'','admin'),
	 (4,'Service','${backendPath}/src/main/java/${packagePath}/${moduleName}/service/${ClassName}Service.java','Service','package ${package}.${moduleName}.service;

#if($ChildClassName)
import com.github.yulichang.extension.mapping.base.MPJDeepService;
import ${package}.${moduleName}.entity.${ChildClassName}Entity;
#else
import com.baomidou.mybatisplus.extension.service.IService;
#end
import ${package}.${moduleName}.entity.${ClassName}Entity;

#if($ChildClassName)
public interface ${ClassName}Service extends MPJDeepService<${ClassName}Entity> {
    Boolean saveDeep(${ClassName}Entity ${className});

    Boolean updateDeep(${ClassName}Entity ${className});

    Boolean removeDeep(Long[] ids);

    Boolean removeChild(Long[] ids);
#else
public interface ${ClassName}Service extends IService<${ClassName}Entity> {
#end

}','2023-02-23 01:16:53','2023-06-04 10:35:25','0',1,' ',' '),
	 (5,'ServiceImpl','${backendPath}/src/main/java/${packagePath}/${moduleName}/service/impl/${ClassName}ServiceImpl.java','ServiceImpl','package ${package}.${moduleName}.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ${package}.${moduleName}.entity.${ClassName}Entity;
import ${package}.${moduleName}.mapper.${ClassName}Mapper;
import ${package}.${moduleName}.service.${ClassName}Service;
import org.springframework.stereotype.Service;
#if($ChildClassName)
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import ${package}.${moduleName}.entity.${ChildClassName}Entity;
import ${package}.${moduleName}.mapper.${ChildClassName}Mapper;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import java.util.Objects;
#end
/**
 * ${tableComment}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
#if($ChildClassName)
@RequiredArgsConstructor
#end
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}Entity> implements ${ClassName}Service {
#if($ChildClassName)
  private final ${ChildClassName}Mapper ${childClassName}Mapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDeep(${ClassName}Entity ${className}) {
        baseMapper.insert(${className});
        for (${ChildClassName}Entity  ${childClassName} : ${className}.get${ChildClassName}List()) {
            ${childClassName}.$str.setProperty($childField)(${className}.$str.getProperty($mainField)());
            ${childClassName}Mapper.insert( ${childClassName});
        }

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDeep(${ClassName}Entity ${className}) {
        baseMapper.updateById(${className});
        for (${ChildClassName}Entity  ${childClassName} : ${className}.get${ChildClassName}List()) {
#set($getChildPkName=$str.getProperty(${pk.attrName}))
            if (Objects.isNull(${childClassName}.$getChildPkName())) {
                ${childClassName}.$str.setProperty($childField)(${className}.$str.getProperty($mainField)());
                ${childClassName}Mapper.insert(${childClassName});
            } else {
                ${childClassName}Mapper.updateById(${childClassName});
            }
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeDeep(Long[] ids) {
        baseMapper.deleteBatchIds(CollUtil.toList(ids));
        ${childClassName}Mapper.delete(Wrappers.<${ChildClassName}Entity>lambdaQuery().in(${ChildClassName}Entity::$str.getProperty($childField), ids));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeChild(Long[] ids) {
        ${childClassName}Mapper.deleteBatchIds(CollUtil.toList(ids));
        return Boolean.TRUE;
    }
#end
}','2023-02-23 01:17:36','2023-08-27 23:29:58','0',1,' ',' '),
	 (6,'实体','${backendPath}/src/main/java/${packagePath}/${moduleName}/entity/${ClassName}Entity.java','Entity','package ${package}.${moduleName}.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
#if($isTenant)
import com.pig4cloud.pigx.common.core.util.TenantTable;
#end
#foreach($import in $importList)
import $import;
#end
#if($ChildClassName)
import com.alibaba.excel.annotation.ExcelIgnore;
import com.github.yulichang.annotation.EntityMapping;
import java.util.List;
#end

/**
 * ${tableComment}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
#if($isTenant)
@TenantTable
#end
@TableName("${tableName}")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "${tableComment}")
public class ${ClassName}Entity extends Model<${ClassName}Entity> {

#foreach ($field in $fieldList)
#if(${field.fieldComment})#set($comment=${field.fieldComment})#else #set($comment=${field.attrName})#end

	/**
	* $comment
	*/
#if($field.primaryPk == ''1'')
    @TableId(type = IdType.ASSIGN_ID)
#end
#if($field.autoFill == ''INSERT'')
	@TableField(fill = FieldFill.INSERT)
#elseif($field.autoFill == ''INSERT_UPDATE'')
	@TableField(fill = FieldFill.INSERT_UPDATE)
#elseif($field.autoFill == ''UPDATE'')
	@TableField(fill = FieldFill.UPDATE)
#end
#if($field.fieldName == ''del_flag'')
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
#end
    @Schema(description="$comment"#if($field.hidden),hidden=$field.hidden#end)
#if($field.formType == ''checkbox'')
    private ${field.attrType}[] $field.attrName;
#else
    private $field.attrType $field.attrName;
#end    
#end
#if($ChildClassName)
    @ExcelIgnore
    @TableField(exist = false)
    @EntityMapping(thisField = "$mainField", joinField = "$childField")
    private List<${ChildClassName}Entity> ${childClassName}List;
#end
}','2023-02-23 01:17:53','2024-05-12 15:15:47','0',1,'','admin'),
	 (7,'Mapper','${backendPath}/src/main/java/${packagePath}/${moduleName}/mapper/${ClassName}Mapper.java','Mapper','package ${package}.${moduleName}.mapper;

import com.pig4cloud.pigx.common.data.datascope.PigxBaseMapper;
import ${package}.${moduleName}.entity.${ClassName}Entity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ${ClassName}Mapper extends PigxBaseMapper<${ClassName}Entity> {


}','2023-02-23 01:18:18','2023-08-13 13:52:50','0',1,' ',' '),
	 (8,'Mapper.xml','${backendPath}/src/main/resources/mapper/${ClassName}Mapper.xml','Mapper.xml','<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="${package}.${moduleName}.mapper.${ClassName}Mapper">

  <resultMap id="${className}Map" type="${package}.${moduleName}.entity.${ClassName}Entity">
#foreach ($field in $fieldList)
	    #if($field.primaryPk == ''1'')
        <id property="$field.attrName" column="$field.fieldName"/>
      #else
        <result property="$field.attrName" column="$field.fieldName"/>
      #end
#end
  </resultMap>
</mapper>','2023-02-23 01:18:35','2023-09-23 21:36:09','0',1,' ',' '),
	 (9,'权限菜单','${backendPath}/menu/${functionName}_menu.sql','menu.sql','-- 该脚本不要直接执行， 注意维护菜单的父节点ID 默认 父节点-1 , 默认租户 1
#set($menuId=${dateTool.getSystemTime()})

-- 菜单SQL
insert into sys_menu ( menu_id,parent_id, path, permission, menu_type, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (${menuId}, ''-1'', ''/${moduleName}/${functionName}/index'', '''', ''0'', ''icon-bangzhushouji'', ''0'', null , ''8'', null , ''${tableComment}管理'', 1);

-- 菜单对应按钮SQL
insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (${math.add($menuId,1)},${menuId}, ''${moduleName}_${functionName}_view'', ''1'', null, ''1'',  ''0'', null, ''0'', null, ''${tableComment}查看'', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (${math.add($menuId,2)},${menuId}, ''${moduleName}_${functionName}_add'', ''1'', null, ''1'',  ''0'', null, ''1'', null, ''${tableComment}新增'', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon,  del_flag, create_time, sort_order, update_time, name, tenant_id)
values (${math.add($menuId,3)},${menuId}, ''${moduleName}_${functionName}_edit'', ''1'', null, ''1'',  ''0'', null, ''2'', null, ''${tableComment}修改'', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (${math.add($menuId,4)},${menuId}, ''${moduleName}_${functionName}_del'', ''1'', null, ''1'',  ''0'', null, ''3'', null, ''${tableComment}删除'', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (${math.add($menuId,5)},${menuId}, ''${moduleName}_${functionName}_export'', ''1'', null, ''1'',  ''0'', null, ''3'', null, ''导入导出'', 1);','2023-02-23 01:19:08','2023-08-27 23:16:31','0',1,' ',' '),
	 (10,'api.ts','${frontendPath}/src/api/${moduleName}/${functionName}.ts','api.ts','import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: ''/${moduleName}/${functionName}/page'',
    method: ''get'',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: ''/${moduleName}/${functionName}'',
    method: ''post'',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: ''/${moduleName}/${functionName}/'' + id,
    method: ''get''
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: ''/${moduleName}/${functionName}'',
    method: ''delete'',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: ''/${moduleName}/${functionName}'',
    method: ''put'',
    data: obj
  })
}

#if($ChildClassName)
export function delChildObj(ids?: Object) {
  return request({
    url: ''/${moduleName}/${functionName}/child'',
    method: ''delete'',
    data: ids
  })
}
#end','2023-02-23 01:19:23','2023-06-04 10:34:17','0',1,' ',' ');
INSERT INTO public.gen_template (id,template_name,generator_path,template_desc,template_code,create_time,update_time,del_flag,tenant_id,create_by,update_by) VALUES
	 (11,'表格','${frontendPath}/src/views/${moduleName}/${functionName}/index.vue','表格不含i18n','<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
#if($queryList)
      <el-row v-show="showSearch">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
#foreach($field in $queryList)
#if($field.queryFormType == ''select'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-select v-model="state.queryForm.${field.attrName}" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end">
       #if($field.fieldDict)
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in ${field.fieldDict}" :key="index"></el-option>
         #else
              <el-option label="请选择">0</el-option>
         #end
            </el-select>
      </el-form-item>
#elseif($field.queryFormType == ''date'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
      <el-date-picker type="date" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="state.queryForm.${field.attrName}"></el-date-picker>
      </el-form-item>
#elseif($field.queryFormType == ''datetime'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}" >
            <el-date-picker type="datetime" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="state.queryForm.${field.attrName}"></el-date-picker>
      </el-form-item>
      </el-col>
#else
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}" >
        <el-input placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="state.queryForm.${field.attrName}" />
      </el-form-item>
#end
#end
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList">
              查询
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>
#end
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()"
            v-auth="''${moduleName}_${functionName}_add''">
            新 增
          </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary"
            v-auth="''${moduleName}_${functionName}_del''" @click="handleDelete(selectObjs)">
            删除
          </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="''${moduleName}_${functionName}_export''"
                @exportExcel="exportExcel" class="ml10 mr20" style="float: right;"
            @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row>
      <el-table :data="state.dataList" v-loading="state.loading" border 
        :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="#" width="40" />
      #foreach($field in $gridList)
        #if($field.fieldDict)
          <el-table-column prop="${field.attrName}" label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" show-overflow-tooltip>
      <template #default="scope">
                <dict-tag :options="$field.fieldDict" :value="scope.row.${field.attrName}"></dict-tag>
            </template>
          </el-table-column>
        #else
          <el-table-column prop="${field.attrName}" label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" #if(${field.gridSort} == ''1'')sortable="custom"#end show-overflow-tooltip/>
        #end
     #end
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button icon="edit-pen" text type="primary" v-auth="''${moduleName}_${functionName}_edit''"
              @click="formDialogRef.openDialog(scope.row.${pk.attrName})">编辑</el-button>
            <el-button icon="delete" text type="primary" v-auth="''${moduleName}_${functionName}_del''" @click="handleDelete([scope.row.${pk.attrName}])">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

  </div>
</template>

<script setup lang="ts" name="system${ClassName}">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchList, delObjs } from "/@/api/${moduleName}/${functionName}";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { useDict } from ''/@/hooks/dict'';

// 引入组件
const FormDialog = defineAsyncComponent(() => import(''./form.vue''));
// 定义查询字典
#set($fieldDict=[])
#foreach($field in $queryList)
  #if($field.fieldDict)
    #set($void=$fieldDict.add($field.fieldDict))
  #end
#end

#foreach($field in $gridList)
  #if($field.fieldDict)
    #set($void=$fieldDict.add($field.fieldDict))
  #end
#end
#if($fieldDict)
const { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))
#end
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  pageList: fetchList
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile,
	tableStyle
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields()
  // 清空多选
  selectObjs.value = []
  getDataList()
}

// 导出excel
const exportExcel = () => {
  downBlobFile(''/${moduleName}/${functionName}/export'', Object.assign(state.queryForm, { ids: selectObjs }), ''${functionName}.xlsx'')
}

// 多选事件
const selectionChangHandle = (objs: { $pk.attrName: string }[]) => {
  selectObjs.value = objs.map(({ $pk.attrName }) => $pk.attrName);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm(''此操作将永久删除'');
  } catch {
    return;
  }

  try {
    await delObjs(ids);
    getDataList();
    useMessage().success(''删除成功'');
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};
</script>','2023-02-23 01:19:35','2023-08-29 14:27:53','0',1,'','admin'),
	 (12,'表单','${frontendPath}/src/views/${moduleName}/${functionName}/form.vue','表单不含i18n','<template>
    <el-dialog :title="form.${pk.attrName} ? ''编辑'' : ''新增''" v-model="visible"
      :close-on-click-modal="false" draggable>
      <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
       <el-row :gutter="24">
#foreach($field in $formList)
#if($field.attrName != ${pk.attrName})
#if($formLayout == 1)
    <el-col :span="24" class="mb20">
#elseif($formLayout == 2)
    <el-col :span="12" class="mb20">
#end
#if($field.formType == ''text'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
        <el-input v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"/>
      </el-form-item>
      </el-col>
#elseif($field.formType == ''textarea'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
        <el-input type="textarea" v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"/>
      </el-form-item>
      </el-col>
#elseif($field.formType == ''select'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
          <el-select v-model="form.${field.attrName}" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end">
     #if($field.fieldDict)
            <el-option :value="item.value" :label="item.label" v-for="(item, index) in ${field.fieldDict}" :key="index"></el-option>
       #end
     #if(!$field.fieldDict)
            <el-option label="请选择">0</el-option>
       #end
          </el-select>
        </el-form-item>
      </el-col>
#elseif($field.formType == ''radio'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-radio-group v-model="form.${field.attrName}">
     #if($field.fieldDict)
             <el-radio :label="item.value" v-for="(item, index) in ${field.fieldDict}" border :key="index">{{ item.label }}
            </el-radio>
       #else
           <el-radio label="${field.fieldComment}" border>${field.fieldComment}</el-radio>
       #end
            </el-radio-group>
        </el-form-item>
      </el-col>
#elseif($field.formType == ''checkbox'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-checkbox-group v-model="form.${field.attrName}">
     #if($field.fieldDict)
						<el-checkbox :label="item.value" v-for="(item, index) in ${field.fieldDict}" :key="index">{{ item.label }}</el-checkbox>
       #end
     #if(!$field.fieldDict)
                <el-checkbox label="启用" name="type"></el-checkbox>
                <el-checkbox label="禁用" name="type"></el-checkbox>
       #end
            </el-checkbox-group>
        </el-form-item>
      </el-col>
#elseif($field.formType == ''date'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
      <el-date-picker type="date" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="form.${field.attrName}" :value-format="dateStr"></el-date-picker>
      </el-form-item>
      </el-col>
#elseif($field.formType == ''datetime'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-date-picker type="datetime" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="form.${field.attrName}" :value-format="dateTimeStr"></el-date-picker>
      </el-form-item>
      </el-col>

#elseif($field.formType == ''number'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
        <el-input-number :min="1" :max="1000" v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"></el-input-number>
      </el-form-item>
    </el-col>
#elseif($field.formType == ''upload-file'')
  <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
    <upload-file v-model:imageUrl="form.${field.attrName}"></upload-file>
  </el-form-item>
  </el-col>
#elseif($field.formType == ''upload-img'')
  <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
    <upload-img v-model="form.${field.attrName}"></upload-img>
  </el-form-item>
  </el-col>
#elseif($field.formType == ''editor'')
  <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
    <editor v-if="visible" v-model:get-html="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"></editor>
  </el-form-item>
  </el-col>
#end

#if(!$field.formType)
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${column.attrName}">
        <el-input v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"/>
      </el-form-item>
    </el-col>
#end
#end
#end
			</el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
        </span>
      </template>
    </el-dialog>
</template>

<script setup lang="ts" name="${ClassName}Dialog">
import { useDict } from ''/@/hooks/dict'';
import { useMessage } from "/@/hooks/message";
import { getObj, addObj, putObj } from ''/@/api/${moduleName}/${functionName}''
import { rule } from ''/@/utils/validate'';
const emit = defineEmits([''refresh'']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false)
const loading = ref(false)
// 定义字典
#set($fieldDict=[])
#foreach($field in $gridList)
	#if($field.fieldDict)
		#set($void=$fieldDict.add($field.fieldDict))
	#end
#end
#if($fieldDict)
const { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))
#end

// 提交表单数据
const form = reactive({
#if(!$formList.contains(${pk.attrName}))
		${pk.attrName}:'''',
#end
#foreach($field in $formList)
#if($field.formType == ''number'')
		${field.attrName}: 0,
#elseif($field.formType == ''checkbox'')
    ${field.attrName}: [],
#else
	  ${field.attrName}: '''',
#end
#end
});

// 定义校验规则
const dataRules = ref({
#foreach($field in $formList)
#if($field.formRequired == ''1'' && $field.formValidator)
    ${field.attrName}: [{required: true, message: ''${field.fieldComment}不能为空'', trigger: ''blur''}, { validator: rule.${field.formValidator}, trigger: ''blur'' }],
#elseif($field.formRequired == ''1'')
        ${field.attrName}: [{required: true, message: ''${field.fieldComment}不能为空'', trigger: ''blur''}],
#elseif($field.formValidator)
        ${field.attrName}: [{ validator: rule.${field.formValidator}, trigger: ''blur'' }],
#end
#end
})

// 打开弹窗
const openDialog = (id: string) => {
  visible.value = true
  form.${pk.attrName} = ''''

  // 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

  // 获取${className}信息
  if (id) {
    form.${pk.attrName} = id
    get${className}Data(id)
  }
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
    loading.value = true;
		form.${pk.attrName} ? await putObj(form) : await addObj(form);
		useMessage().success(form.${pk.attrName} ? ''修改成功'' : ''添加成功'');
		visible.value = false;
		emit(''refresh'');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
    loading.value = false;
  }
};


// 初始化表单数据
const get${className}Data = (id: string) => {
  // 获取数据
  loading.value = true
  getObj(id).then((res: any) => {
    Object.assign(form, res.data)
  }).finally(() => {
    loading.value = false
  })
};

// 暴露变量
defineExpose({
  openDialog
});
</script>','2023-02-23 01:19:48','2023-12-07 13:20:22','0',1,'','admin'),
	 (13,'i18n英文模板','${frontendPath}/src/views/${moduleName}/${functionName}/i18n/en.ts','i18n英文模板','export default {
   ${functionName}: {
        index: ''#'',
        import${className}Tip: ''import ${ClassName}'',
#foreach($field in $fieldList)
        ${field.attrName}: ''${field.attrName}'',
#end
#foreach($field in $fieldList)
        input$str.pascalCase(${field.attrName})Tip: ''input ${field.attrName}'',
#end
    }
}','2023-02-23 01:20:25','2023-06-04 10:49:25','0',1,'','admin'),
	 (14,'i18n中文模板','${frontendPath}/src/views/${moduleName}/${functionName}/i18n/zh-cn.ts','i18n中文模板','export default {
   ${functionName}: {
        index: ''#'',
        import${className}Tip: ''导入${tableComment}'',
#foreach($field in $fieldList)
        ${field.attrName}: ''#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end'',
#end
#foreach($field in $fieldList)
        input$str.pascalCase(${field.attrName})Tip: ''请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end'',
#end
    }
}','2023-02-23 01:20:40','2023-06-04 10:49:28','0',1,'','admin'),
	 (15,'子实体','${backendPath}/src/main/java/${packagePath}/${moduleName}/entity/${ChildClassName}Entity.java','子表实体对象','package ${package}.${moduleName}.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
#if($isChildTenant)
import com.pig4cloud.pigx.common.core.util.TenantTable;
#end
import lombok.Data;
import lombok.EqualsAndHashCode;
#foreach($import in $importList)
import $import;
#end

/**
 * ${tableComment}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
#if($isChildTenant)
@TenantTable
#end
@TableName("${childTableName}")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "${childTableName}")
public class ${ChildClassName}Entity extends Model<${ChildClassName}Entity> {

#foreach ($field in $childFieldList)
#if(${field.fieldComment})#set($comment=${field.fieldComment})#else #set($comment=${field.attrName})#end
	/**
	* $comment
	*/
#if($field.primaryPk == ''1'')
	@TableId(type = IdType.ASSIGN_ID)
#end
#if($field.autoFill == ''INSERT'')
	@TableField(fill = FieldFill.INSERT)
#elseif($field.autoFill == ''INSERT_UPDATE'')
	@TableField(fill = FieldFill.INSERT_UPDATE)
#elseif($field.autoFill == ''UPDATE'')
	@TableField(fill = FieldFill.UPDATE)
#end
#if($field.fieldName == ''del_flag'')
  @TableLogic
	@TableField(fill = FieldFill.INSERT)
#end
	@Schema(description="$comment"#if($field.hidden),hidden=$field.hidden#end)
#if($field.formType == ''checkbox'')
   private ${field.attrType}[] $field.attrName;
#else
   private $field.attrType $field.attrName;
#end 
#end
}','2023-06-01 11:07:14','2024-05-12 15:07:04','0',1,'','admin'),
	 (16,'主子Contoller','${backendPath}/src/main/java/${packagePath}/${moduleName}/controller/${ClassName}Controller.java','子表Controller对象','package ${package}.${moduleName}.controller;

#if($queryList)
import cn.hutool.core.util.StrUtil;
#end
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import ${package}.${moduleName}.entity.${ClassName}Entity;
import ${package}.${moduleName}.entity.${ChildClassName}Entity;
import ${package}.${moduleName}.service.${ClassName}Service;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
#if($isSpringBoot3)
import org.springdoc.core.annotations.ParameterObject;
#else
import org.springdoc.api.annotations.ParameterObject;
#end
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * ${tableComment}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/${functionName}" )
@Tag(description = "${functionName}" , name = "${tableComment}管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ${ClassName}Controller {

    private final  ${ClassName}Service ${className}Service;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ${className} ${tableComment}
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_view'')" )
    public R get${ClassName}Page(@ParameterObject Page page, @ParameterObject ${ClassName}Entity ${className}) {
        LambdaQueryWrapper<${ClassName}Entity> wrapper = Wrappers.lambdaQuery();
#foreach ($field in $queryList)
#set($getAttrName=$str.getProperty($field.attrName))
#set($var="${className}.$getAttrName()")
#if($field.attrType == ''String'')
#set($expression="StrUtil.isNotBlank")
#else
#set($expression="Objects.nonNull")
#end
#if($field.queryType == ''='')
		wrapper.eq($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''like'' )
		wrapper.like($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''!-'' )
		wrapper.ne($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''>'' )
		wrapper.gt($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''<'' )
		wrapper.lt($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''>='' )
		wrapper.ge($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''<='' )
		wrapper.le($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''left like'' )
		wrapper.likeLeft($expression($var),${ClassName}Entity::$getAttrName,$var);
#elseif( $field.queryType == ''right like'' )
		wrapper.likeRight($expression($var),${ClassName}Entity::$getAttrName,$var);
#end
#end
        return R.ok(${className}Service.page(page, wrapper));
    }

    /**
     * 通过id查询${tableComment}
     * @param ${pk.attrName} id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{${pk.attrName}}" )
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_view'')" )
    public R getById(@PathVariable("${pk.attrName}" ) ${pk.attrType} ${pk.attrName}) {
        return R.ok(${className}Service.getByIdDeep(${pk.attrName}));
    }

    /**
     * 新增${tableComment}
     * @param ${className} ${tableComment}
     * @return R
     */
    @Operation(summary = "新增${tableComment}" , description = "新增${tableComment}" )
    @SysLog("新增${tableComment}" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_add'')" )
    public R save(@RequestBody ${ClassName}Entity ${className}) {
        return R.ok(${className}Service.saveDeep(${className}));
    }

    /**
     * 修改${tableComment}
     * @param ${className} ${tableComment}
     * @return R
     */
    @Operation(summary = "修改${tableComment}" , description = "修改${tableComment}" )
    @SysLog("修改${tableComment}" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_edit'')" )
    public R updateById(@RequestBody ${ClassName}Entity ${className}) {
        return R.ok(${className}Service.updateDeep(${className}));
    }

    /**
     * 通过id删除${tableComment}
     * @param ids ${pk.attrName}列表
     * @return R
     */
    @Operation(summary = "通过id删除${tableComment}" , description = "通过id删除${tableComment}" )
    @SysLog("通过id删除${tableComment}" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_del'')" )
    public R removeById(@RequestBody ${pk.attrType}[] ids) {
        return R.ok(${className}Service.removeDeep(ids));
    }

    /**
     * 通过id删除${tableComment}子表数据
     * @param ids ${pk.attrName}列表
     * @return R
     */
    @Operation(summary = "通过id删除${tableComment}子表数据" , description = "通过id删除${tableComment}子表数据" )
    @SysLog("通过id删除${tableComment}子表数据" )
    @DeleteMapping("/child")
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_del'')" )
    public R removeChild(@RequestBody ${pk.attrType}[] ids) {
        return R.ok(${className}Service.removeChild(ids));
    }

    /**
     * 导出excel 表格
     * @param ${className} 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission(''${moduleName}_${functionName}_export'')" )
    public List<${ClassName}Entity> export(${ClassName}Entity ${className},${pk.attrType}[] ids) {
        return ${className}Service.list(Wrappers.lambdaQuery(${className}).in(ArrayUtil.isNotEmpty(ids), ${ClassName}Entity::$str.getProperty($pk.attrName), ids));
    }
}','2023-06-01 11:25:28','2023-10-29 12:17:52','0',1,'','admin'),
	 (17,'主子表单','${frontendPath}/src/views/${moduleName}/${functionName}/form.vue','子表表单','<template>
  <el-drawer :title="form.${pk.attrName} ? (detail ? ''详情'' : ''编辑'') : ''添加''" v-model="visible" size="50%">
      <el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="90px" :disabled="detail" v-loading="loading">
        <el-row :gutter="24">
#foreach($field in $formList)
#if($field.attrName != ${pk.attrName})
#if($formLayout == 1)
    <el-col :span="24" class="mb20">
#elseif($formLayout == 2)
    <el-col :span="12" class="mb20">
#end
#if($field.formType == ''text'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
        <el-input v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"/>
      </el-form-item>
      </el-col>
#elseif($field.formType == ''textarea'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
        <el-input type="textarea" v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"/>
      </el-form-item>
      </el-col>
#elseif($field.formType == ''select'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
          <el-select v-model="form.${field.attrName}" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end">
     #if($field.fieldDict)
            <el-option :value="item.value" :label="item.label" v-for="(item, index) in ${field.fieldDict}" :key="index"></el-option>
       #end
     #if(!$field.fieldDict)
            <el-option label="请选择">0</el-option>
       #end
          </el-select>
        </el-form-item>
      </el-col>
#elseif($field.formType == ''radio'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-radio-group v-model="form.${field.attrName}">
     #if($field.fieldDict)
             <el-radio :label="item.value" v-for="(item, index) in ${field.fieldDict}" border :key="index">{{ item.label }}
            </el-radio>
       #else
           <el-radio label="${field.fieldComment}" border>${field.fieldComment}</el-radio>
       #end
            </el-radio-group>
        </el-form-item>
      </el-col>
#elseif($field.formType == ''checkbox'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-checkbox-group v-model="form.${field.attrName}">
     #if($field.fieldDict)
                <el-checkbox :label="item.value" v-for="(item, index) in ${field.fieldDict}" :key="index">{{ item.label }}</el-checkbox>
       #end
     #if(!$field.fieldDict)
                <el-checkbox label="启用" name="type"></el-checkbox>
                <el-checkbox label="禁用" name="type"></el-checkbox>
       #end
            </el-checkbox-group>
        </el-form-item>
      </el-col>
#elseif($field.formType == ''date'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
      <el-date-picker type="date" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="form.${field.attrName}" :value-format="dateStr"></el-date-picker>
      </el-form-item>
      </el-col>
#elseif($field.formType == ''datetime'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-date-picker type="datetime" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="form.${field.attrName}" :value-format="dateTimeStr"></el-date-picker>
      </el-form-item>
      </el-col>
#elseif($field.formType == ''number'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
        <el-input-number :min="1" :max="1000" v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"></el-input-number>
      </el-form-item>
    </el-col>
#elseif($field.formType == ''upload-file'')
  <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
    <upload-file  v-model="form.${field.attrName}"></upload-file>
  </el-form-item>
  </el-col>
#elseif($field.formType == ''upload-img'')
  <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
    <upload-img v-model:imageUrl="form.${field.attrName}"></upload-img>
  </el-form-item>
  </el-col>
#elseif($field.formType == ''editor'')
  <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
    <editor v-model:get-html="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"></editor>
  </el-form-item>
  </el-col>
#end
#if(!$field.formType)
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${column.attrName}">
        <el-input v-model="form.${field.attrName}" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end"/>
      </el-form-item>
    </el-col>
#end
#end
#end
    </el-row>
  <el-row :gutter="24">
    <sc-form-table
      v-model="form.${childClassName}List"
      :addTemplate="childTemp"
      @delete="deleteChild"
      placeholder="暂无数据"
    >
#set($ignoreColumnList = ["create_by","create_time","update_by","update_time","del_flag","tenant_id"])
#foreach($field in $childFieldList)
#if($field.primaryPk == ''1'')
#elseif($ignoreColumnList.contains(${field.fieldName}))
#elseif($field.attrName == $childField)
#else  
      <el-table-column label="${field.fieldComment}" prop="${field.attrName}">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${childClassName}List.${$index}.${field.attrName}`" :rules="[{ required: true, trigger: ''blur'' }]">
            <el-input v-model="row.${field.attrName}" style="width: 100%" />
          </el-form-item>
        </template>
      </el-table-column>
#end
#end
    </sc-form-table>
  </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
        </span>
      </template>
    </el-drawer>
</template>

<script setup lang="ts" name="${ClassName}Dialog">
import { useDict } from ''/@/hooks/dict'';
import { rule } from ''/@/utils/validate'';
import { useMessage } from "/@/hooks/message";
import { getObj, addObj, putObj, delChildObj } from ''/@/api/${moduleName}/${functionName}''
const scFormTable = defineAsyncComponent(() => import(''/@/components/FormTable/index.vue''));
const emit = defineEmits([''refresh'']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const detail = ref(false);

// 定义字典
#set($fieldDict=[])
#foreach($field in $gridList)
	#if($field.fieldDict)
		#set($void=$fieldDict.add($field.fieldDict))
	#end
#end
#if($fieldDict)
const { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))
#end

// 提交表单数据
const form = reactive({
#if(!$formList.contains(${pk.attrName}))
		${pk.attrName}:'''',
#end
#foreach($field in $formList)
#if($field.formType == ''number'')
		${field.attrName}: 0,
#elseif($field.formType == ''checkbox'')
    ${field.attrName}: [],
#else
	  ${field.attrName}: '''',
#end
#end
	  ${childClassName}List:[],
});

const childTemp = reactive({
  #foreach($field in $childFieldList)
    ${field.attrName}: '''',
  #end
})

// 定义校验规则
const dataRules = ref({
#foreach($field in $formList)
#if($field.formRequired == ''1'' && $field.formValidator)
    ${field.attrName}: [{required: true, message: ''${field.fieldComment}不能为空'', trigger: ''blur''}, { validator: rule.${field.formValidator}, trigger: ''blur'' }],
#elseif($field.formRequired == ''1'')
        ${field.attrName}: [{required: true, message: ''${field.fieldComment}不能为空'', trigger: ''blur''}],
#elseif($field.formValidator)
        ${field.attrName}: [{ validator: rule.${field.formValidator}, trigger: ''blur'' }],
#end
#end
})

// 打开弹窗
const openDialog = (id: string, isDetail: boolean) => {
  visible.value = true
  detail.value = isDetail
  form.${pk.attrName} = ''''

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
    form.${childClassName}List = [];
  });

  // 获取${className}信息
  if (id) {
    form.${pk.attrName} = id
    get${ClassName}Data(id)
  }
};

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => {});
  if (!valid) return false;

  try {
    loading.value = true;
    form.${pk.attrName} ? await putObj(form) : await addObj(form);
    useMessage().success(form.${pk.attrName} ? ''修改成功'' : ''添加成功'');
    visible.value = false;
    emit(''refresh'');
  } catch (err: any) {
    useMessage().error(err.msg);
  } finally {
    loading.value = false;
  }
};
#foreach ($field in $childFieldList)
#if($field.primaryPk == ''1'')
#set($childPkName=$field.attrName)
#end
#end
// 删除子表数据
const deleteChild = async (obj: { $childPkName: string }) => {
  if (obj.$childPkName) {
    try {
      await delChildObj([obj.$childPkName]);
      useMessage().success(''删除成功'');
    } catch (err: any) {
      useMessage().error(err.msg);
    }
  }
};

// 初始化表单数据
const get${ClassName}Data = (id: string) => {
  // 获取数据
  getObj(id).then((res: any) => {
    Object.assign(form, res.data)
  })
};

// 暴露变量
defineExpose({
  openDialog
});
</script>','2023-06-01 15:42:46','2023-12-07 13:22:29','0',1,'','admin'),
	 (18,'主子表格','${frontendPath}/src/views/${moduleName}/${functionName}/index.vue','子表单表格','<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
#if($queryList)
      <el-row v-show="showSearch">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
#foreach($field in $queryList)
#if($field.queryFormType == ''select'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
            <el-select v-model="state.queryForm.${field.attrName}" placeholder="请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end">
       #if($field.fieldDict)
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in ${field.fieldDict}" :key="index"></el-option>
         #else
              <el-option label="请选择">0</el-option>
         #end
            </el-select>
      </el-form-item>
#elseif($field.queryFormType == ''date'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}">
      <el-date-picker type="date" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="state.queryForm.${field.attrName}"></el-date-picker>
      </el-form-item>
#elseif($field.queryFormType == ''datetime'')
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}" >
            <el-date-picker type="datetime" placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="state.queryForm.${field.attrName}"></el-date-picker>
      </el-form-item>
      </el-col>
#else
      <el-form-item label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" prop="${field.attrName}" >
        <el-input placeholder="请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" v-model="state.queryForm.${field.attrName}"
          style="max-width: 180px" />
      </el-form-item>
#end
#end
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList">
              查询
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>
#end
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()"
            v-auth="''${moduleName}_${functionName}_add''">
            新 增
          </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary"
            v-auth="''${moduleName}_${functionName}_del''" @click="handleDelete(selectObjs)">
            删除
          </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="''${moduleName}_${functionName}_export''"
                @exportExcel="exportExcel" class="ml10 mr20" style="float: right;"
            @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row>
      <el-table :data="state.dataList" v-loading="state.loading" border 
        :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle"
        @selection-change="selectionChangeHandle" @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="#" width="40" />
      #foreach($field in $gridList)
        #if($field.fieldDict)
          <el-table-column prop="${field.attrName}" label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" show-overflow-tooltip>
      <template #default="scope">
                <dict-tag :options="$field.fieldDict" :value="scope.row.${field.attrName}"></dict-tag>
            </template>
          </el-table-column>
        #else
          <el-table-column prop="${field.attrName}" label="#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end" #if(${field.gridSort} == ''1'')sortable="custom"#end show-overflow-tooltip/>
        #end
     #end
        <el-table-column label="操作" width="200">
          <template #default="scope">
          #if($ChildClassName)
            <el-button text type="primary" icon="view" v-auth="''sys_role_edit''" @click="formDialogRef.openDialog(scope.row.${pk.attrName}, true)">
              详情
            </el-button>
          #end
            <el-button icon="edit-pen" text type="primary" v-auth="''${moduleName}_${functionName}_edit''"
              @click="formDialogRef.openDialog(scope.row.${pk.attrName})">编辑</el-button>
            <el-button icon="delete" text type="primary" v-auth="''${moduleName}_${functionName}_del''" @click="handleDelete([scope.row.${pk.attrName}])">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
  </div>
</template>

<script setup lang="ts" name="system${ClassName}">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchList, delObjs } from "/@/api/${moduleName}/${functionName}";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { useDict } from ''/@/hooks/dict'';
// 引入组件
const FormDialog = defineAsyncComponent(() => import(''./form.vue''));

// 定义查询字典
#set($fieldDict=[])
#foreach($field in $queryList)
  #if($field.fieldDict)
    #set($void=$fieldDict.add($field.fieldDict))
  #end
#end

#foreach($field in $gridList)
  #if($field.fieldDict)
    #set($void=$fieldDict.add($field.fieldDict))
  #end
#end
#if($fieldDict)
const { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))
#end
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  pageList: fetchList
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile,
  tableStyle
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields()
  // 清空多选
  selectObjs.value = []
  getDataList()
}

// 导出excel
const exportExcel = () => {
  downBlobFile(''/${moduleName}/${functionName}/export'', Object.assign(state.queryForm, { ids: selectObjs }), ''${functionName}.xlsx'')
}

// 多选事件
const selectionChangeHandle = (objs: { $pk.attrName: string }[]) => {
  selectObjs.value = objs.map(({ $pk.attrName }) => $pk.attrName);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm(''此操作将永久删除'');
  } catch {
    return;
  }

  try {
    await delObjs(ids);
    getDataList();
    useMessage().success(''删除成功'');
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};
</script>','2023-06-01 15:43:31','2023-08-29 10:53:23','0',1,' ',' '),
	 (19,'子Mapper','${backendPath}/src/main/java/${packagePath}/${moduleName}/mapper/${ChildClassName}Mapper.java','子Mapper','package ${package}.${moduleName}.mapper;

import com.pig4cloud.pigx.common.data.datascope.PigxBaseMapper;
#if($ChildClassName)
import ${package}.${moduleName}.entity.${ChildClassName}Entity;
#else
import ${package}.${moduleName}.entity.${ClassName}Entity;
#end
import org.apache.ibatis.annotations.Mapper;

@Mapper
#if($ChildClassName)
public interface ${ChildClassName}Mapper extends PigxBaseMapper<${ChildClassName}Entity> {
#else
public interface ${ClassName}Mapper extends PigxBaseMapper<${ClassName}Entity> {
#end

}','2023-02-23 01:18:18','2023-08-07 09:54:36','0',1,' ',' ');
INSERT INTO public.gen_template_group (group_id,template_id) VALUES
	 (1,3),
	 (1,4),
	 (1,5),
	 (1,6),
	 (1,7),
	 (1,8),
	 (1,9),
	 (1,10),
	 (1,11),
	 (1,12);
INSERT INTO public.gen_template_group (group_id,template_id) VALUES
	 (2,4),
	 (2,5),
	 (2,6),
	 (2,7),
	 (2,8),
	 (2,9),
	 (2,10),
	 (2,15),
	 (2,16),
	 (2,17);
INSERT INTO public.gen_template_group (group_id,template_id) VALUES
	 (2,18),
	 (2,19);
INSERT INTO public.sys_area (id,pid,"name",letter,adcode,"location",area_sort,area_status,area_type,hot,city_code,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (1,0,'全国','',100000,'',NULL,'1','0','0','','2023-11-09 03:50:52','2023-11-09 03:50:52','admin','2024-02-17 12:45:08','0'),
	 (2,100000,'北京市','',110000,'',NULL,'1','1','1','','2023-11-09 03:50:52','2023-11-09 03:50:52','admin','2024-02-17 12:45:12','0'),
	 (3,100000,'天津市','',120000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (4,100000,'河北省','',130000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (5,100000,'山西省','',140000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (6,100000,'内蒙古自治区','',150000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (7,100000,'辽宁省','',210000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (8,100000,'吉林省','',220000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52','admin','2024-02-16 23:16:23','0'),
	 (9,100000,'黑龙江省','',230000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (10,100000,'上海市','',310000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0');
INSERT INTO public.sys_area (id,pid,"name",letter,adcode,"location",area_sort,area_status,area_type,hot,city_code,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (11,100000,'江苏省','',320000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (12,100000,'浙江省','',330000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (13,100000,'安徽省','',340000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (14,100000,'福建省','',350000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (15,100000,'江西省','',360000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (16,100000,'山东省','',370000,'',100,'1','1','1','','2023-11-09 03:50:52','2023-11-09 03:50:52','admin','2024-02-17 13:13:25','0'),
	 (17,100000,'河南省','',410000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (18,100000,'湖北省','',420000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (19,100000,'湖南省','',430000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (20,100000,'广东省','',440000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0');
INSERT INTO public.sys_area (id,pid,"name",letter,adcode,"location",area_sort,area_status,area_type,hot,city_code,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (21,100000,'广西壮族自治区','',450000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (22,100000,'海南省','',460000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (23,100000,'重庆市','',500000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (24,100000,'四川省','',510000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (25,100000,'贵州省','',520000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (26,100000,'云南省','',530000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (27,100000,'西藏自治区','',540000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (28,100000,'陕西省','',610000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (29,100000,'甘肃省','',620000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0'),
	 (30,100000,'青海省','',630000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0');
INSERT INTO public.sys_area (id,pid,"name",letter,adcode,"location",area_sort,area_status,area_type,hot,city_code,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (31,100000,'宁夏回族自治区','',640000,'',NULL,'1','1','0','','2023-11-09 03:50:52','2023-11-09 03:50:52',NULL,NULL,'0');
INSERT INTO public.sys_dept (dept_id,"name",sort_order,create_by,update_by,create_time,update_time,del_flag,parent_id,tenant_id) VALUES
	 (1,'总裁办',1,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:07:49','0',0,1),
	 (2,'技术部',2,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',1,1),
	 (3,'市场部',3,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',1,1),
	 (4,'销售部',4,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',1,1),
	 (5,'财务部',5,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',1,1),
	 (6,'人事行政部',6,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:53:36','1',1,1),
	 (7,'研发部',7,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',2,1),
	 (8,'UI设计部',11,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',7,1),
	 (9,'产品部',12,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',2,1),
	 (10,'渠道部',13,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',3,1);
INSERT INTO public.sys_dept (dept_id,"name",sort_order,create_by,update_by,create_time,update_time,del_flag,parent_id,tenant_id) VALUES
	 (11,'推广部',14,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',3,1),
	 (12,'客服部',15,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',4,1),
	 (13,'财务会计部',16,'admin','admin','2023-04-03 13:04:47','2023-04-03 13:04:47','0',5,1),
	 (14,'审计风控部',17,'admin','admin','2023-04-03 13:04:47','2023-04-03 14:06:57','0',5,1);
INSERT INTO public.sys_dict (id,dict_type,description,create_by,update_by,create_time,update_time,remarks,system_flag,del_flag,tenant_id) VALUES
	 (1,'log_type','日志类型',' ',' ','2019-03-19 11:06:44','2019-03-19 11:06:44','异常、正常','1','0',1),
	 (2,'social_type','社交登录',' ',' ','2019-03-19 11:09:44','2019-03-19 11:09:44','微信、QQ','1','0',1),
	 (3,'job_type','定时任务类型',' ',' ','2019-03-19 11:22:21','2019-03-19 11:22:21','quartz','1','0',1),
	 (4,'job_status','定时任务状态',' ',' ','2019-03-19 11:24:57','2019-03-19 11:24:57','发布状态、运行状态','1','0',1),
	 (5,'job_execute_status','定时任务执行状态',' ',' ','2019-03-19 11:26:15','2019-03-19 11:26:15','正常、异常','1','0',1),
	 (6,'misfire_policy','定时任务错失执行策略',' ',' ','2019-03-19 11:27:19','2019-03-19 11:27:19','周期','1','0',1),
	 (7,'gender','性别',' ',' ','2019-03-27 13:44:06','2019-03-27 13:44:06','微信用户性别','1','0',1),
	 (8,'subscribe','订阅状态',' ',' ','2019-03-27 13:48:33','2019-03-27 13:48:33','公众号订阅状态','1','0',1),
	 (9,'response_type','回复',' ',' ','2019-03-28 21:29:21','2019-03-28 21:29:21','微信消息是否已回复','1','0',1),
	 (10,'param_type','参数配置',' ',' ','2019-04-29 18:20:47','2019-04-29 18:20:47','检索、原文、报表、安全、文档、消息、其他','1','0',1);
INSERT INTO public.sys_dict (id,dict_type,description,create_by,update_by,create_time,update_time,remarks,system_flag,del_flag,tenant_id) VALUES
	 (11,'status_type','租户状态',' ',' ','2019-05-15 16:31:08','2019-05-15 16:31:08','租户状态','1','0',1),
	 (12,'dict_type','字典类型',' ',' ','2019-05-16 14:16:20','2019-05-16 14:20:16','系统类不能修改','1','0',1),
	 (13,'channel_type','支付类型',' ',' ','2019-05-16 14:16:20','2019-05-16 14:20:16','系统类不能修改','1','0',1),
	 (14,'grant_types','授权类型',' ',' ','2019-08-13 07:34:10','2019-08-13 07:34:10',NULL,'1','0',1),
	 (15,'style_type','前端风格',' ',' ','2020-02-07 03:49:28','2020-02-07 03:50:40','0-Avue 1-element','1','0',1),
	 (16,'captcha_flag_types','验证码开关',' ',' ','2020-11-18 06:53:25','2020-11-18 06:53:25','是否校验验证码','1','0',1),
	 (17,'enc_flag_types','前端密码加密',' ',' ','2020-11-18 06:54:44','2020-11-18 06:54:44','前端密码是否加密传输','1','0',1),
	 (18,'lock_flag','用户状态','admin',' ','2023-02-01 16:55:31',NULL,NULL,'1','0',1),
	 (19,'ds_config_type','数据连接类型','admin',' ','2023-02-06 18:36:59',NULL,NULL,'1','0',1),
	 (20,'common_status','通用状态','admin',' ','2023-02-09 11:02:08',NULL,NULL,'1','0',1);
INSERT INTO public.sys_dict (id,dict_type,description,create_by,update_by,create_time,update_time,remarks,system_flag,del_flag,tenant_id) VALUES
	 (21,'app_social_type','app社交登录','admin',' ','2023-02-10 11:11:06',NULL,'app社交登录','1','0',1),
	 (22,'yes_no_type','是否','admin',' ','2023-02-20 23:25:04',NULL,NULL,'1','0',1),
	 (23,'repType','微信消息类型','admin',' ','2023-02-24 15:08:25',NULL,NULL,'0','0',1),
	 (24,'leave_status','请假状态','admin',' ','2023-03-02 22:50:15',NULL,NULL,'0','0',1),
	 (25,'schedule_type','日程类型','admin',' ','2023-03-06 14:49:18',NULL,NULL,'0','0',1),
	 (26,'schedule_status','日程状态','admin',' ','2023-03-06 14:52:57',NULL,NULL,'0','0',1),
	 (27,'ds_type','代码生成器支持的数据库类型','admin',' ','2023-03-12 09:57:59',NULL,NULL,'1','0',1),
	 (28,'message_type','消息类型','admin',' ','2023-10-27 10:29:48',NULL,NULL,'1','0',1),
	 (29,'sensitive_type','敏感词类型','admin',' ','2023-10-27 10:29:48',NULL,NULL,'1','0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (1,1,'9','异常','log_type','日志异常',1,' ',' ','2019-03-19 11:08:59','2019-03-25 12:49:13','','0',1),
	 (2,1,'0','正常','log_type','日志正常',0,' ',' ','2019-03-19 11:09:17','2019-03-25 12:49:18','','0',1),
	 (3,2,'WX','微信','social_type','微信登录',0,' ',' ','2019-03-19 11:10:02','2019-03-25 12:49:36','','0',1),
	 (4,2,'QQ','QQ','social_type','QQ登录',1,' ',' ','2019-03-19 11:10:14','2019-03-25 12:49:36','','0',1),
	 (5,3,'1','java类','job_type','java类',1,' ',' ','2019-03-19 11:22:37','2019-03-25 12:49:36','','0',1),
	 (6,3,'2','spring bean','job_type','spring bean容器实例',2,' ',' ','2019-03-19 11:23:05','2019-03-25 12:49:36','','0',1),
	 (7,3,'9','其他','job_type','其他类型',9,' ',' ','2019-03-19 11:23:31','2019-03-25 12:49:36','','0',1),
	 (8,3,'3','Rest 调用','job_type','Rest 调用',3,' ',' ','2019-03-19 11:23:57','2019-03-25 12:49:36','','0',1),
	 (9,3,'4','jar','job_type','jar类型',4,' ',' ','2019-03-19 11:24:20','2019-03-25 12:49:36','','0',1),
	 (10,4,'1','未发布','job_status','未发布',1,' ',' ','2019-03-19 11:25:18','2019-03-25 12:49:36','','0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (11,4,'2','运行中','job_status','运行中',2,' ',' ','2019-03-19 11:25:31','2019-03-25 12:49:36','','0',1),
	 (12,4,'3','暂停','job_status','暂停',3,' ',' ','2019-03-19 11:25:42','2019-03-25 12:49:36','','0',1),
	 (13,5,'0','正常','job_execute_status','正常',0,' ',' ','2019-03-19 11:26:27','2019-03-25 12:49:36','','0',1),
	 (14,5,'1','异常','job_execute_status','异常',1,' ',' ','2019-03-19 11:26:41','2019-03-25 12:49:36','','0',1),
	 (15,6,'1','错失周期立即执行','misfire_policy','错失周期立即执行',1,' ',' ','2019-03-19 11:27:45','2019-03-25 12:49:36','','0',1),
	 (16,6,'2','错失周期执行一次','misfire_policy','错失周期执行一次',2,' ',' ','2019-03-19 11:27:57','2019-03-25 12:49:36','','0',1),
	 (17,6,'3','下周期执行','misfire_policy','下周期执行',3,' ',' ','2019-03-19 11:28:08','2019-03-25 12:49:36','','0',1),
	 (18,7,'1','男','gender','微信-男',0,' ',' ','2019-03-27 13:45:13','2019-03-27 13:45:13','微信-男','0',1),
	 (19,7,'2','女','gender','女-微信',1,' ',' ','2019-03-27 13:45:34','2019-03-27 13:45:34','女-微信','0',1),
	 (20,7,'0','未知','gender','性别未知',3,' ',' ','2019-03-27 13:45:57','2019-03-27 13:45:57','性别未知','0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (21,8,'0','未关注','subscribe','公众号-未关注',0,' ',' ','2019-03-27 13:49:07','2019-03-27 13:49:07','公众号-未关注','0',1),
	 (22,8,'1','已关注','subscribe','公众号-已关注',1,' ',' ','2019-03-27 13:49:26','2019-03-27 13:49:26','公众号-已关注','0',1),
	 (23,9,'0','未回复','response_type','微信消息-未回复',0,' ',' ','2019-03-28 21:29:47','2019-03-28 21:29:47','微信消息-未回复','0',1),
	 (24,9,'1','已回复','response_type','微信消息-已回复',1,' ',' ','2019-03-28 21:30:08','2019-03-28 21:30:08','微信消息-已回复','0',1),
	 (25,10,'1','检索','param_type','检索',0,' ',' ','2019-04-29 18:22:17','2019-04-29 18:22:17','检索','0',1),
	 (26,10,'2','原文','param_type','原文',0,' ',' ','2019-04-29 18:22:27','2019-04-29 18:22:27','原文','0',1),
	 (27,10,'3','报表','param_type','报表',0,' ',' ','2019-04-29 18:22:36','2019-04-29 18:22:36','报表','0',1),
	 (28,10,'4','安全','param_type','安全',0,' ',' ','2019-04-29 18:22:46','2019-04-29 18:22:46','安全','0',1),
	 (29,10,'5','文档','param_type','文档',0,' ',' ','2019-04-29 18:22:56','2019-04-29 18:22:56','文档','0',1),
	 (30,10,'6','消息','param_type','消息',0,' ',' ','2019-04-29 18:23:05','2019-04-29 18:23:05','消息','0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (31,10,'9','其他','param_type','其他',0,' ',' ','2019-04-29 18:23:16','2019-04-29 18:23:16','其他','0',1),
	 (32,10,'0','默认','param_type','默认',0,' ',' ','2019-04-29 18:23:30','2019-04-29 18:23:30','默认','0',1),
	 (33,11,'0','正常','status_type','状态正常',0,' ',' ','2019-05-15 16:31:34','2019-05-16 22:30:46','状态正常','0',1),
	 (34,11,'9','冻结','status_type','状态冻结',1,' ',' ','2019-05-15 16:31:56','2019-05-16 22:30:50','状态冻结','0',1),
	 (35,12,'1','系统类','dict_type','系统类字典',0,' ',' ','2019-05-16 14:20:40','2019-05-16 14:20:40','不能修改删除','0',1),
	 (36,12,'0','业务类','dict_type','业务类字典',0,' ',' ','2019-05-16 14:20:59','2019-05-16 14:20:59','可以修改','0',1),
	 (37,2,'GITEE','码云','social_type','码云',2,' ',' ','2019-06-28 09:59:12','2019-06-28 09:59:12','码云','0',1),
	 (38,2,'OSC','开源中国','social_type','开源中国登录',2,' ',' ','2019-06-28 10:04:32','2019-06-28 10:04:32','','0',1),
	 (39,14,'password','密码模式','grant_types','支持oauth密码模式',0,' ',' ','2019-08-13 07:35:28','2019-08-13 07:35:28',NULL,'0',1),
	 (40,14,'authorization_code','授权码模式','grant_types','oauth2 授权码模式',1,' ',' ','2019-08-13 07:36:07','2019-08-13 07:36:07',NULL,'0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (41,14,'client_credentials','客户端模式','grant_types','oauth2 客户端模式',2,' ',' ','2019-08-13 07:36:30','2019-08-13 07:36:30',NULL,'0',1),
	 (42,14,'refresh_token','刷新模式','grant_types','oauth2 刷新token',3,' ',' ','2019-08-13 07:36:54','2019-08-13 07:36:54',NULL,'0',1),
	 (43,14,'implicit','简化模式','grant_types','oauth2 简化模式',4,' ',' ','2019-08-13 07:39:32','2019-08-13 07:39:32',NULL,'0',1),
	 (44,15,'0','Avue','style_type','Avue风格',0,' ',' ','2020-02-07 03:52:52','2020-02-07 03:52:52','','0',1),
	 (45,15,'1','element','style_type','element-ui',1,' ',' ','2020-02-07 03:53:12','2020-02-07 03:53:12','','0',1),
	 (46,16,'0','关','captcha_flag_types','不校验验证码',0,' ',' ','2020-11-18 06:53:58','2020-11-18 06:53:58','不校验验证码 -0','0',1),
	 (47,16,'1','开','captcha_flag_types','校验验证码',1,' ',' ','2020-11-18 06:54:15','2020-11-18 06:54:15','不校验验证码-1','0',1),
	 (48,17,'0','否','enc_flag_types','不加密',0,' ',' ','2020-11-18 06:55:31','2020-11-18 06:55:31','不加密-0','0',1),
	 (49,17,'1','是','enc_flag_types','加密',1,' ',' ','2020-11-18 06:55:51','2020-11-18 06:55:51','加密-1','0',1),
	 (50,13,'MERGE_PAY','聚合支付','channel_type','聚合支付',1,' ',' ','2019-05-30 19:08:08','2019-06-18 13:51:53','聚合支付','0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (51,2,'CAS','CAS登录','social_type','CAS 单点登录系统',3,' ',' ','2022-02-18 13:56:25','2022-02-18 13:56:28',NULL,'0',1),
	 (52,2,'DINGTALK','钉钉','social_type','钉钉',3,' ',' ','2022-02-18 13:56:25','2022-02-18 13:56:28',NULL,'0',1),
	 (53,2,'WEIXIN_CP','企业微信','social_type','企业微信',3,' ',' ','2022-02-18 13:56:25','2022-02-18 13:56:28',NULL,'0',1),
	 (54,15,'2','APP','style_type','uview风格',1,' ',' ','2020-02-07 03:53:12','2020-02-07 03:53:12','','0',1),
	 (55,13,'ALIPAY_WAP','支付宝支付','channel_type','支付宝支付',1,' ',' ','2019-05-30 19:08:08','2019-06-18 13:51:53','聚合支付','0',1),
	 (56,13,'WEIXIN_MP','微信支付','channel_type','微信支付',1,' ',' ','2019-05-30 19:08:08','2019-06-18 13:51:53','聚合支付','0',1),
	 (57,14,'mobile','mobile','grant_types','移动端登录',5,'admin',' ','2023-01-29 17:21:42',NULL,NULL,'0',1),
	 (58,18,'0','有效','lock_flag','有效',0,'admin',' ','2023-02-01 16:56:00',NULL,NULL,'0',1),
	 (59,18,'9','禁用','lock_flag','禁用',1,'admin',' ','2023-02-01 16:56:09',NULL,NULL,'0',1),
	 (60,15,'4','vue3','style_type','element-plus',4,'admin',' ','2023-02-06 13:52:43',NULL,NULL,'0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (61,19,'0','主机','ds_config_type','主机',0,'admin',' ','2023-02-06 18:37:23',NULL,NULL,'0',1),
	 (62,19,'1','JDBC','ds_config_type','jdbc',2,'admin',' ','2023-02-06 18:37:34',NULL,NULL,'0',1),
	 (63,20,'false','否','common_status','否',1,'admin',' ','2023-02-09 11:02:39',NULL,NULL,'0',1),
	 (64,20,'true','是','common_status','是',2,'admin',' ','2023-02-09 11:02:52',NULL,NULL,'0',1),
	 (65,21,'MINI','小程序','app_social_type','小程序登录',0,'admin',' ','2023-02-10 11:11:41',NULL,NULL,'0',1),
	 (66,22,'0','否','yes_no_type','0',0,'admin',' ','2023-02-20 23:35:23',NULL,'0','0',1),
	 (67,22,'1','是','yes_no_type','1',0,'admin',' ','2023-02-20 23:35:37',NULL,'1','0',1),
	 (69,23,'text','文本','repType','文本',0,'admin',' ','2023-02-24 15:08:45',NULL,NULL,'0',1),
	 (70,23,'image','图片','repType','图片',0,'admin',' ','2023-02-24 15:08:56',NULL,NULL,'0',1),
	 (71,23,'voice','语音','repType','语音',0,'admin',' ','2023-02-24 15:09:08',NULL,NULL,'0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (72,23,'video','视频','repType','视频',0,'admin',' ','2023-02-24 15:09:18',NULL,NULL,'0',1),
	 (73,23,'shortvideo','小视频','repType','小视频',0,'admin',' ','2023-02-24 15:09:29',NULL,NULL,'0',1),
	 (74,23,'location','地理位置','repType','地理位置',0,'admin',' ','2023-02-24 15:09:41',NULL,NULL,'0',1),
	 (75,23,'link','链接消息','repType','链接消息',0,'admin',' ','2023-02-24 15:09:49',NULL,NULL,'0',1),
	 (76,23,'event','事件推送','repType','事件推送',0,'admin',' ','2023-02-24 15:09:57',NULL,NULL,'0',1),
	 (77,24,'0','未提交','leave_status','未提交',0,'admin',' ','2023-03-02 22:50:45',NULL,'未提交','0',1),
	 (78,24,'1','审批中','leave_status','审批中',0,'admin',' ','2023-03-02 22:50:57',NULL,'审批中','0',1),
	 (79,24,'2','完成','leave_status','完成',0,'admin',' ','2023-03-02 22:51:06',NULL,'完成','0',1),
	 (80,24,'9','驳回','leave_status','驳回',0,'admin',' ','2023-03-02 22:51:20',NULL,NULL,'0',1),
	 (81,25,'record','日程记录','schedule_type','日程记录',0,'admin',' ','2023-03-06 14:50:01',NULL,NULL,'0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (82,25,'plan','计划','schedule_type','计划类型',0,'admin',' ','2023-03-06 14:50:29',NULL,NULL,'0',1),
	 (83,26,'0','计划中','schedule_status','日程状态',0,'admin',' ','2023-03-06 14:53:18',NULL,NULL,'0',1),
	 (84,26,'1','已开始','schedule_status','已开始',0,'admin',' ','2023-03-06 14:53:33',NULL,NULL,'0',1),
	 (85,26,'3','已结束','schedule_status','已结束',0,'admin',' ','2023-03-06 14:53:41',NULL,NULL,'0',1),
	 (86,27,'mysql','mysql','ds_type','mysql',0,'admin',' ','2023-03-12 09:58:11',NULL,NULL,'0',1),
	 (87,27,'pg','pg','ds_type','pg',1,'admin',' ','2023-03-12 09:58:20',NULL,NULL,'0',1),
	 (88,27,'oracle','oracle','ds_type','oracle',2,'admin',' ','2023-03-12 09:58:29',NULL,NULL,'0',1),
	 (89,27,'mssql','mssql','ds_type','mssql',3,'admin',' ','2023-03-12 09:58:42',NULL,NULL,'0',1),
	 (90,27,'db2','db2','ds_type','db2',4,'admin',' ','2023-03-12 09:58:53',NULL,NULL,'0',1),
	 (91,27,'dm','达梦','ds_type','达梦',5,'admin',' ','2023-03-12 09:59:07',NULL,NULL,'0',1);
INSERT INTO public.sys_dict_item (id,dict_id,item_value,"label",dict_type,description,sort_order,create_by,update_by,create_time,update_time,remarks,del_flag,tenant_id) VALUES
	 (92,27,'highgo','瀚高','ds_type','瀚高数据库',5,'admin',' ','2023-03-12 09:59:07',NULL,NULL,'0',1),
	 (93,28,'0','公告','message_type','主页公告显示',0,'admin',' ','2023-10-27 10:30:14',NULL,NULL,'0',1),
	 (94,28,'1','站内信','message_type','右上角显示',1,'admin',' ','2023-10-27 10:30:47',NULL,NULL,'0',1),
	 (95,29,'0','黑名单','sensitive_type','敏感词类型',0,'admin',' ','2023-10-27 10:30:14',NULL,NULL,'0',1),
	 (96,29,'1','白名单','sensitive_type','敏感词类型',1,'admin',' ','2023-10-27 10:30:47',NULL,NULL,'0',1);
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (1,'router.permissionManagement','权限管理','Permission Management','','2023-02-14 02:03:59','',NULL,'0'),
	 (2,'router.userManagement','用户管理','User Management','admin','2023-02-14 10:39:08','',NULL,'0'),
	 (3,'router.menuManagement','菜单管理','Menu Management','admin','2023-02-15 23:14:39','',NULL,'0'),
	 (4,'router.roleManagement','角色管理','Role Management','admin','2023-02-15 23:15:51','',NULL,'0'),
	 (5,'router.departmentManagement','部门管理','Department Management','admin','2023-02-15 23:16:52','',NULL,'0'),
	 (6,'router.tenantManagement','租户管理','Tenant Management','admin','2023-02-24 10:08:29','',NULL,'0'),
	 (7,'router.postManagement','岗位管理','Post Management','admin','2023-02-24 10:12:58','',NULL,'0'),
	 (8,'router.systemManagement','系统管理','System Management','admin','2023-02-24 10:13:34','admin','2023-02-24 10:58:30','0'),
	 (9,'router.operationLog','操作日志','Operation Log','admin','2023-02-24 10:14:47','',NULL,'0'),
	 (10,'router.dictManagement','字典管理','Dictionary Management','admin','2023-02-24 10:16:21','',NULL,'0');
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (11,'router.parameterManagement','参数管理','Parameter Management','admin','2023-02-24 10:17:04','',NULL,'0'),
	 (12,'router.codeGeneration','代码生成','Code Generation','admin','2023-02-24 10:19:16','',NULL,'0'),
	 (13,'router.terminalManagement','终端管理','Terminal Management','admin','2023-02-24 10:21:45','',NULL,'0'),
	 (14,'router.keyManagement','密钥管理','Key Management','admin','2023-02-24 10:22:52','',NULL,'0'),
	 (15,'router.tokenManagement','令牌管理','Token Management','admin','2023-02-24 10:23:22','',NULL,'0'),
	 (16,'router.quartzManagement','Quartz管理','Quartz Management','admin','2023-02-24 10:24:32','',NULL,'0'),
	 (17,'router.metadataManagement','元数据管理','Metadata Management','admin','2023-02-24 10:25:11','',NULL,'0'),
	 (18,'router.documentExtension','文档扩展','Document Extension','admin','2023-02-24 10:27:23','',NULL,'0'),
	 (19,'router.fileManagement','文件管理','File Management','admin','2023-02-24 10:28:44','',NULL,'0'),
	 (20,'router.platformDevelopment','开发平台','Platform Development','admin','2023-02-24 10:29:28','',NULL,'0');
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (21,'router.dataSourceManagement','数据源管理','Data Source Management','admin','2023-02-24 10:30:33','admin','2023-03-06 14:33:20','0'),
	 (22,'router.formDesign','表单设计','Form Design','admin','2023-02-24 10:31:33','admin','2023-03-06 14:33:28','0'),
	 (23,'router.appManagement','APP管理','App Management','admin','2023-02-24 10:33:22','',NULL,'0'),
	 (24,'router.customerManagement','客户管理','Customer Management','admin','2023-02-24 10:35:30','',NULL,'0'),
	 (25,'router.appRole','APP角色','App Role','admin','2023-02-24 10:36:17','',NULL,'0'),
	 (26,'router.appPermission','APP权限','App Permission','admin','2023-02-24 10:36:59','admin','2023-02-24 10:37:47','0'),
	 (27,'router.appKey','APP秘钥','App Key','admin','2023-02-24 10:36:59','admin','2023-02-24 10:40:27','0'),
	 (28,'router.internationalizationManagement','国际化管理','Internationalization Management','admin','2023-02-24 10:36:59','',NULL,'0'),
	 (29,'router.auditLog','审计日志','Audit Log','admin','2023-02-24 10:36:59','',NULL,'0'),
	 (30,'router.systemMonitoring','系统监控','System Monitoring','admin','2023-02-24 10:36:59','',NULL,'0');
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (31,'router.generatePages','生成页面','Generate Pages','admin','2023-02-24 10:44:04','',NULL,'0'),
	 (32,'router.templateManagement','模板管理','Template Management','admin','2023-02-24 10:44:31','',NULL,'0'),
	 (33,'router.templateGroup','模板分组','Template Group','admin','2023-02-24 10:45:10','',NULL,'0'),
	 (34,'router.fieldManagement','字段管理','Field Management','admin','2023-02-24 10:46:04','admin','2023-03-07 14:27:48','0'),
	 (35,'router.wechatPlatform','公众号平台','WeChat Platform','admin','2023-02-24 10:48:51','admin','2023-02-24 11:03:41','0'),
	 (36,'router.accountManagement','账号管理','Account Management','admin','2023-02-24 10:13:34','',NULL,'0'),
	 (37,'router.menuSettings','菜单设置','Menu Settings','admin','2023-02-24 14:02:22','',NULL,'0'),
	 (38,'router.fanManagement','粉丝管理','Fan Management','admin','2023-02-24 14:03:44','',NULL,'0'),
	 (39,'router.messageManagement','消息管理','Message Management','admin','2023-02-24 14:03:45','',NULL,'0'),
	 (40,'router.paymentSystem','支付系统','Payment System','admin','2023-02-24 14:03:46','',NULL,'0');
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (41,'router.checkoutCounter','收银台','Checkout Counter','admin','2023-02-24 14:03:47','',NULL,'0'),
	 (42,'router.mediaManagement','素材管理','Media Management','admin','2023-02-24 14:03:48','',NULL,'0'),
	 (43,'router.paymentChannel','支付渠道','Payment Channel','admin','2023-02-24 14:03:49','',NULL,'0'),
	 (44,'router.productOrder','商品订单','Product Order','admin','2023-02-24 14:03:50','',NULL,'0'),
	 (45,'router.notificationRecord','通知记录','Notification Record','admin','2023-02-24 14:03:51','',NULL,'0'),
	 (46,'router.refundOrder','退款订单','Refund Order','admin','2023-02-24 14:03:52','',NULL,'0'),
	 (47,'router.paymentOrder','支付订单','Payment Order','admin','2023-02-24 14:03:53','',NULL,'0'),
	 (48,'router.autoReply','自动回复','Auto Reply','admin','2023-02-24 14:03:54','',NULL,'0'),
	 (49,'router.operationalData','运营数据','Operational Data','admin','2023-02-24 14:03:55','',NULL,'0'),
	 (50,'router.logManagement','日志管理','Log Management','admin',NULL,'',NULL,'0');
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (51,'router.collaborativeOffice','协同办公','Collaborative Office','admin',NULL,'',NULL,'0'),
	 (52,'router.modelManagement','模型管理','Model Management','admin',NULL,'',NULL,'0'),
	 (53,'router.modelDiagramView','模型图查看','Model Diagram View','admin',NULL,'',NULL,'0'),
	 (54,'router.processManagement','流程管理','Process Management','admin',NULL,'',NULL,'0'),
	 (55,'router.leaveWorkOrder','请假工单','Leave Work Order','admin',NULL,'',NULL,'0'),
	 (56,'router.todoTask','待办任务','Todo Task','admin',NULL,'',NULL,'0'),
	 (57,'router.tagManagement','标签管理','Tag Management','admin',NULL,'',NULL,'0'),
	 (58,'router.articleInformation','文章资讯','Article Information',' ','2023-08-10 13:40:09',' ',NULL,'0'),
	 (59,'router.articleCategory','文章分类','Article Category',' ','2023-08-10 13:40:48',' ',NULL,'0'),
	 (60,'router.interfaceSettings','界面设置','Interface Settings',' ','2023-08-10 13:41:21',' ',NULL,'0');
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (61,'router.bottomNavigation','底部导航','Bottom Navigation',' ','2023-08-10 13:41:54',' ',NULL,'0'),
	 (62,'router.cacheMonitoring','缓存监控','Cache Monitoring',' ','2023-08-10 13:42:35',' ',NULL,'0'),
	 (63,'rotuer. initiateProcess','发起流程','Initiate Process',' ','2023-08-10 13:44:23',' ',NULL,'0'),
	 (64,'router.taskManagement','任务管理','Task Management',' ','2023-08-10 13:44:53',' ',NULL,'0'),
	 (65,'router.myInitiations','我的发起','My Initiations',' ','2023-08-10 13:45:17',' ',NULL,'0'),
	 (66,'router.copiedtoMe','抄送给我','Copied to Me',' ','2023-08-10 13:45:46',' ',NULL,'0'),
	 (67,'router.completedTasks','我的已办','Completed Tasks',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0'),
	 (68,'router.bizPlatform','业务平台','Biz Platform',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0'),
	 (69,'router.baseTools','基础工具','Base Tools',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0'),
	 (70,'router.route','路由管理','Route Management',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0');
INSERT INTO public.sys_i18n (id,"name",zh_cn,en,create_by,create_time,update_by,update_time,del_flag) VALUES
	 (71,'router.datav','大屏看板','Data Visual',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0'),
	 (72,'router.bi','数据报表','Bi Report',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0'),
	 (73,'router.message','信息推送','Message',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0'),
	 (74,'router.sensitiveWords','敏感词管理','Sensitive words',' ','2023-08-10 13:46:37',' ','2023-08-10 13:47:09','0');
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (1000,'权限管理',NULL,'/system',NULL,2000,'iconfont icon-icon-','1',0,'0','0','0','','2018-09-28 08:29:53','admin','2023-11-01 16:39:55','0',1),
	 (1100,'用户管理',NULL,'/admin/system/user/index',NULL,1000,'ele-User','1',1,'0','0','0','','2017-11-02 22:24:37','admin','2023-11-01 16:40:44','0',1),
	 (1101,'用户新增','sys_user_add',NULL,NULL,1100,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 09:52:09',' ','2021-05-25 03:12:55','0',1),
	 (1102,'用户修改','sys_user_edit',NULL,NULL,1100,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 09:52:48',' ','2021-05-25 03:12:55','0',1),
	 (1103,'用户删除','sys_user_del',NULL,NULL,1100,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 09:54:01',' ','2021-05-25 03:12:55','0',1),
	 (1104,'导入导出','sys_user_export',NULL,NULL,1100,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 09:54:01',' ','2021-05-25 03:12:55','0',1),
	 (1200,'菜单管理',NULL,'/admin/system/menu/index',NULL,1000,'iconfont icon-caidan','1',2,'0','0','0','','2017-11-08 09:57:27','admin','2023-11-01 16:40:39','0',1),
	 (1201,'菜单新增','sys_menu_add',NULL,NULL,1200,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 10:15:53',' ','2021-05-25 03:12:55','0',1),
	 (1202,'菜单修改','sys_menu_edit',NULL,NULL,1200,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 10:16:23',' ','2021-05-25 03:12:55','0',1),
	 (1203,'菜单删除','sys_menu_del',NULL,NULL,1200,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 10:16:43',' ','2021-05-25 03:12:55','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (1300,'角色管理',NULL,'/admin/system/role/index',NULL,1000,'iconfont icon-gerenzhongxin','1',3,'0',NULL,'0','','2017-11-08 10:13:37','admin','2023-11-01 16:40:35','0',1),
	 (1301,'角色新增','sys_role_add',NULL,NULL,1300,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 10:14:18',' ','2021-05-25 03:12:55','0',1),
	 (1302,'角色修改','sys_role_edit',NULL,NULL,1300,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 10:14:41',' ','2021-05-25 03:12:55','0',1),
	 (1303,'角色删除','sys_role_del',NULL,NULL,1300,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 10:14:59',' ','2021-05-25 03:12:55','0',1),
	 (1304,'分配权限','sys_role_perm',NULL,NULL,1300,NULL,'1',1,'0',NULL,'1',' ','2018-04-20 07:22:55',' ','2021-05-25 03:12:55','0',1),
	 (1305,'角色导入导出','sys_role_export',NULL,NULL,1300,NULL,'1',4,'0',NULL,'1',' ','2022-03-26 15:54:34',' ',NULL,'0',1),
	 (1400,'部门管理',NULL,'/admin/system/dept/index',NULL,1000,'iconfont icon-zidingyibuju','1',4,'0',NULL,'0','','2018-01-20 13:17:19','admin','2023-11-01 16:40:30','0',1),
	 (1401,'部门新增','sys_dept_add',NULL,NULL,1400,NULL,'1',1,'0',NULL,'1',' ','2018-01-20 14:56:16',' ','2021-05-25 03:12:55','0',1),
	 (1402,'部门修改','sys_dept_edit',NULL,NULL,1400,NULL,'1',1,'0',NULL,'1',' ','2018-01-20 14:56:59',' ','2021-05-25 03:12:55','0',1),
	 (1403,'部门删除','sys_dept_del',NULL,NULL,1400,NULL,'1',1,'0',NULL,'1',' ','2018-01-20 14:57:28',' ','2021-05-25 03:12:55','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (1404,'开放互联','sys_connect_sync',NULL,NULL,1400,NULL,'1',1,'0',NULL,'1',' ','2018-01-20 14:57:28',' ','2021-05-25 03:12:55','0',1),
	 (1500,'租户管理',NULL,'/admin/system/tenant/index',NULL,1000,'iconfont icon-shuxingtu','1',9,'0','0','0','','2018-01-20 13:17:19','admin','2023-11-01 16:40:26','0',1),
	 (1501,'租户新增','sys_systenant_add',NULL,NULL,1500,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:56:52','0',1),
	 (1502,'租户修改','sys_systenant_edit',NULL,NULL,1500,'1','1',1,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:56:53','0',1),
	 (1503,'租户删除','sys_systenant_del',NULL,NULL,1500,'1','1',2,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:56:54','0',1),
	 (1504,'租户套餐','sys_systenant_tenantmenu',NULL,NULL,1500,'1','1',1,'0',NULL,'1','admin','2022-12-12 09:01:41',' ','2023-01-11 05:52:51','0',1),
	 (1505,'租户套餐删除','sys_systenantmenu_del',NULL,NULL,1500,'1','1',1,'0',NULL,'1','admin','2022-12-09 14:04:19','admin','2023-01-11 05:52:51','0',1),
	 (1506,'租户套餐编辑','sys_systenantmenu_edit',NULL,NULL,1500,'1','1',1,'0',NULL,'1','admin','2022-12-09 14:04:19','admin','2023-01-11 05:52:51','0',1),
	 (1507,'租户套餐新增','sys_systenantmenu_add',NULL,NULL,1500,'1','1',1,'0',NULL,'1','admin','2022-12-09 14:04:19','admin','2022-12-12 09:02:00','0',1),
	 (1508,'租户套餐导出','sys_systenant_export',NULL,NULL,1500,NULL,'1',0,'0','0','1','admin','2023-03-06 16:28:24',' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (1600,'岗位管理',NULL,'/admin/system/post/index',NULL,1000,'iconfont icon--chaifenhang','1',5,'1','0','0','','2022-03-26 13:04:14','admin','2023-11-01 17:02:55','0',1),
	 (1601,'岗位信息查看','sys_post_view',NULL,NULL,1600,NULL,'1',0,'0',NULL,'1',' ','2022-03-26 13:05:34',' ',NULL,'0',1),
	 (1602,'岗位信息新增','sys_post_add',NULL,NULL,1600,NULL,'1',1,'0',NULL,'1',' ','2022-03-26 13:06:00',' ',NULL,'0',1),
	 (1603,'岗位信息修改','sys_post_edit',NULL,NULL,1600,NULL,'1',2,'0',NULL,'1',' ','2022-03-26 13:06:31',' ','2022-03-26 13:06:38','0',1),
	 (1604,'岗位信息删除','sys_post_del',NULL,NULL,1600,NULL,'1',3,'0',NULL,'1',' ','2022-03-26 13:06:31',' ',NULL,'0',1),
	 (1605,'岗位导入导出','sys_post_export',NULL,NULL,1600,NULL,'1',4,'0',NULL,'1',' ','2022-03-26 13:06:31',' ','2022-03-26 06:32:02','0',1),
	 (2000,'系统管理',NULL,'/admin',NULL,-1,'iconfont icon-quanjushezhi_o','1',1,'0',NULL,'0','','2017-11-07 20:56:00','admin','2023-11-01 16:25:58','0',1),
	 (2001,'日志管理',NULL,'/admin/logs',NULL,2000,'ele-Cloudy','1',1,'0','0','0','admin','2023-03-02 12:26:42','admin','2023-11-01 16:22:08','0',1),
	 (2100,'操作日志',NULL,'/admin/log/index',NULL,2001,'iconfont icon-jinridaiban','1',2,'0','0','0','','2017-11-20 14:06:22','admin','2023-03-02 12:28:57','0',1),
	 (2101,'日志删除','sys_log_del',NULL,NULL,2100,NULL,'1',1,'0',NULL,'1',' ','2017-11-20 20:37:37',' ','2021-05-25 03:12:55','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (2102,'导入导出','sys_log_export',NULL,NULL,2100,NULL,'1',1,'0',NULL,'1',' ','2017-11-08 09:54:01',' ','2021-05-25 03:12:55','0',1),
	 (2103,'审计日志',NULL,'/admin/audit/index',NULL,2001,'iconfont icon-biaodan','1',1,'0','0','0','',NULL,'admin','2023-03-02 12:28:47','0',1),
	 (2104,'审计记录表删除','sys_audit_del',NULL,NULL,2103,'1','1',3,'0',NULL,'1','',NULL,'admin','2023-02-28 20:23:43','0',1),
	 (2105,'导入导出','sys_audit_export',NULL,NULL,2103,'1','1',3,'0',NULL,'1','',NULL,'admin','2023-02-28 20:23:51','0',1),
	 (2106,'敏感数据查看','no_mask',NULL,NULL,2103,'1','1',3,'0',NULL,'1','',NULL,'admin','2023-02-28 20:23:51','0',1),
	 (2200,'字典管理',NULL,'/admin/dict/index',NULL,2000,'iconfont icon-zhongduancanshuchaxun','1',6,'0',NULL,'0','','2017-11-29 11:30:52','admin','2023-02-16 15:24:29','0',1),
	 (2201,'字典删除','sys_dict_del',NULL,NULL,2200,NULL,'1',1,'0',NULL,'1',' ','2017-11-29 11:30:11',' ','2021-05-25 03:12:55','0',1),
	 (2202,'字典新增','sys_dict_add',NULL,NULL,2200,NULL,'1',1,'0',NULL,'1',' ','2018-05-11 22:34:55',' ','2021-05-25 03:12:55','0',1),
	 (2203,'字典修改','sys_dict_edit',NULL,NULL,2200,NULL,'1',1,'0',NULL,'1',' ','2018-05-11 22:36:03',' ','2021-05-25 03:12:55','0',1),
	 (2210,'参数管理',NULL,'/admin/param/index',NULL,2000,'iconfont icon-wenducanshu-05','1',7,'1',NULL,'0','','2019-04-29 22:16:50','admin','2023-02-16 15:24:51','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (2211,'参数新增','sys_syspublicparam_add',NULL,NULL,2210,NULL,'1',1,'0',NULL,'1',' ','2019-04-29 22:17:36',' ','2020-03-24 08:57:11','0',1),
	 (2212,'参数删除','sys_syspublicparam_del',NULL,NULL,2210,NULL,'1',1,'0',NULL,'1',' ','2019-04-29 22:17:55',' ','2020-03-24 08:57:12','0',1),
	 (2213,'参数编辑','sys_syspublicparam_edit',NULL,NULL,2210,NULL,'1',1,'0',NULL,'1',' ','2019-04-29 22:18:14',' ','2020-03-24 08:57:13','0',1),
	 (2300,'代码生成',NULL,'/gen/table/index',NULL,9000,'iconfont icon-zhongduancanshu','1',2,'0','0','0','','2018-01-20 13:17:19','admin','2023-02-20 13:54:35','0',1),
	 (3016,'素材维护','mp_wxmaterial_add',NULL,NULL,3015,NULL,'0',0,'0','0','1','admin','2023-02-27 14:14:07',' ',NULL,'0',1),
	 (2400,'终端管理',NULL,'/admin/client/index',NULL,2000,'iconfont icon-gongju','1',9,'1',NULL,'0','','2018-01-20 13:17:19','admin','2023-02-16 15:25:28','0',1),
	 (2401,'客户端新增','sys_client_add',NULL,NULL,2400,'1','1',1,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2021-05-25 03:12:55','0',1),
	 (2402,'客户端修改','sys_client_edit',NULL,NULL,2400,NULL,'1',1,'0',NULL,'1',' ','2018-05-15 21:37:06',' ','2021-05-25 03:12:55','0',1),
	 (2403,'客户端删除','sys_client_del',NULL,NULL,2400,NULL,'1',1,'0',NULL,'1',' ','2018-05-15 21:39:16',' ','2021-05-25 03:12:55','0',1),
	 (2500,'密钥管理',NULL,'/admin/social/index',NULL,2000,'iconfont icon-quanxian','1',10,'0',NULL,'0','','2018-01-20 13:17:19','admin','2023-02-16 15:26:16','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (2501,'密钥新增','sys_social_details_add',NULL,NULL,2500,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:19','0',1),
	 (2502,'密钥修改','sys_social_details_edit',NULL,NULL,2500,'1','1',1,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:19','0',1),
	 (2503,'密钥删除','sys_social_details_del',NULL,NULL,2500,'1','1',2,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:23','0',1),
	 (2600,'令牌管理',NULL,'/admin/token/index',NULL,2000,'ele-Key','1',11,'0',NULL,'0','','2018-09-04 05:58:41','admin','2023-02-16 15:28:28','0',1),
	 (2601,'令牌删除','sys_token_del',NULL,NULL,2600,NULL,'1',1,'0',NULL,'1',' ','2018-09-04 05:59:50',' ','2020-03-24 08:57:24','0',1),
	 (2800,'Quartz管理',NULL,'/tools/job-manage/index',NULL,9910,'ele-AlarmClock','1',4,'0',NULL,'0','','2018-01-20 13:17:19','admin','2023-11-27 14:52:53','0',1),
	 (2810,'任务新增','job_sys_job_add',NULL,NULL,2800,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:26','0',1),
	 (2820,'任务修改','job_sys_job_edit',NULL,NULL,2800,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:27','0',1),
	 (2830,'任务删除','job_sys_job_del',NULL,NULL,2800,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:28','0',1),
	 (2840,'任务暂停','job_sys_job_shutdown_job',NULL,NULL,2800,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:28','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (2850,'任务开始','job_sys_job_start_job',NULL,NULL,2800,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:29','0',1),
	 (2860,'任务刷新','job_sys_job_refresh_job',NULL,NULL,2800,'1','1',0,'0',NULL,'1',' ','2018-05-15 21:35:18',' ','2020-03-24 08:57:30','0',1),
	 (2870,'执行任务','job_sys_job_run_job',NULL,NULL,2800,'1','1',0,'0',NULL,'1',' ','2019-08-08 15:35:18',' ','2020-03-24 08:57:31','0',1),
	 (2871,'导出','job_sys_job_export',NULL,NULL,2800,NULL,'1',0,'0','0','1','admin','2023-03-06 15:26:13',' ',NULL,'0',1),
	 (2900,'国际化管理',NULL,'/admin/i18n/index',NULL,2000,'iconfont icon-zhongyingzhuanhuan','1',8,'0',NULL,'0','',NULL,'admin','2023-02-16 15:25:18','0',1),
	 (2901,'系统表-国际化查看','sys_i18n_view',NULL,NULL,2900,'1','1',0,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (2902,'系统表-国际化新增','sys_i18n_add',NULL,NULL,2900,'1','1',1,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (2903,'系统表-国际化修改','sys_i18n_edit',NULL,NULL,2900,'1','1',2,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (2904,'系统表-国际化删除','sys_i18n_del',NULL,NULL,2900,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (2905,'导入导出','sys_i18n_export',NULL,NULL,2900,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (2906,'文件管理',NULL,'/admin/file/index',NULL,2000,'ele-Files','1',6,'0',NULL,'0','','2019-06-25 12:44:46','admin','2023-02-16 15:24:42','0',1),
	 (2907,'删除文件','sys_file_del',NULL,NULL,2906,NULL,'1',1,'0',NULL,'1',' ','2019-06-25 13:41:41',' ','2020-03-24 08:58:42','0',1),
	 (2910,'行政区划','','/admin/sysArea/index',NULL,2000,'iconfont icon-neiqianshujuchucun','1',99,'0',NULL,'0','',NULL,'admin','2024-02-16 22:11:03','0',1),
	 (2911,'行政区划表查看','sys_sysArea_view',NULL,NULL,2910,'1','1',0,'0',NULL,'1',' ',NULL,' ','2024-02-17 14:31:09','0',1),
	 (2912,'行政区划表新增','sys_sysArea_add',NULL,NULL,2910,'1','1',1,'0',NULL,'1',' ',NULL,' ','2024-02-17 14:31:16','0',1),
	 (2913,'行政区划表删除','sys_sysArea_del',NULL,NULL,2910,'1','1',3,'0',NULL,'1',' ',NULL,' ','2024-02-17 14:31:21','0',1),
	 (2914,'导入导出','sys_sysArea_export',NULL,NULL,2910,'1','1',3,'0',NULL,'1',' ',NULL,' ','2024-02-17 14:31:26','0',1),
	 (2915,'行政区划表修改','sys_sysArea_edit',NULL,NULL,2910,'1','1',2,'0',NULL,'1',' ',NULL,' ','2024-02-17 14:31:31','0',1),
	 (2920,'敏感词管理','','/admin/sensitive/index',NULL,2000,'iconfont icon-wenducanshu-05','1',12,'0',NULL,'0','',NULL,'admin','2024-07-07 15:09:27','0',1),
	 (2921,'敏感词查看','admin_sysSensitiveWord_view',NULL,NULL,2920,'1','1',0,'0',NULL,'1',' ',NULL,' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (2922,'敏感词新增','admin_sysSensitiveWord_add',NULL,NULL,2920,'1','1',1,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (2923,'敏感词修改','admin_sysSensitiveWord_edit',NULL,NULL,2920,'1','1',2,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (2924,'敏感词删除','admin_sysSensitiveWord_del',NULL,NULL,2920,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (2925,'导入导出','admin_sysSensitiveWord_export',NULL,NULL,2920,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (3000,'公众号平台',NULL,'/mp',NULL,9900,'iconfont icon-putong','1',3,'0','0','0','admin','2023-02-24 10:40:44','admin','2023-11-27 14:52:28','0',1),
	 (3001,'账号管理',NULL,'/biz/mp/wx-account/index',NULL,3000,'iconfont icon-putong','1',0,'0','0','0','admin','2023-02-24 10:43:03',' ','2023-11-01 17:28:07','0',1),
	 (3002,'菜单设置',NULL,'/biz/mp/wx-menu/index',NULL,3000,'iconfont icon--chaifenlie','1',1,'0','0','0','admin','2023-02-24 11:16:32','admin','2023-11-01 17:28:11','0',1),
	 (3003,'删除','mp_wxaccount_del',NULL,NULL,3001,NULL,'0',0,'0','0','1','admin','2023-02-24 13:12:53',' ',NULL,'0',1),
	 (3004,'新增','mp_wxaccount_add',NULL,NULL,3001,NULL,'0',0,'0','0','1','admin','2023-02-24 13:13:04',' ',NULL,'0',1),
	 (3005,'编辑','mp_wxaccount_edit',NULL,NULL,3001,NULL,'0',0,'0','0','1','admin','2023-02-24 13:13:15',' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (3006,'粉丝管理',NULL,'/biz/mp/wx-account-fans/index',NULL,3000,'iconfont icon-tongzhi3','1',2,'0','0','0','admin','2023-02-24 13:28:24','admin','2023-11-01 17:28:15','0',1),
	 (3007,'同步粉丝','mp_wxaccountfans_sync',NULL,NULL,3006,NULL,'0',0,'0','0','1','admin','2023-02-24 14:03:03',' ',NULL,'0',1),
	 (3008,'消息管理',NULL,'/biz/mp/wx-fans-msg/index',NULL,3000,'iconfont icon-tongzhi3','1',6,'0','0','0','admin','2023-02-24 15:24:35','admin','2023-11-01 17:28:21','0',1),
	 (3009,'修改微信消息','mp_wxmsg_edit',NULL,NULL,3008,NULL,'1',0,'0','0','1','admin','2023-02-24 15:41:55','admin','2023-11-01 17:28:28','0',1),
	 (3010,'标签管理',NULL,'/biz/mp/wx-account-tag/index',NULL,3000,'iconfont icon-zidingyibuju','1',3,'0','0','0','admin','2023-03-03 09:49:07','admin','2023-11-01 17:28:32','0',1),
	 (3011,'新增标签','mp_wx_account_tag_add',NULL,NULL,3010,NULL,'1',0,'0','0','1','admin','2023-03-03 09:49:26','admin','2023-03-11 16:29:44','0',1),
	 (3012,'编辑标签','mp_wx_account_tag_edit',NULL,NULL,3010,NULL,'1',0,'0','0','1','admin','2023-03-03 09:49:35','admin','2023-03-11 16:29:50','0',1),
	 (3013,'标签删除','mp_wx_account_tag_del',NULL,NULL,3010,NULL,'1',0,'0','0','1','admin','2023-03-03 09:49:45','admin','2023-03-11 16:29:53','0',1),
	 (3014,'同步标签','mp_wx_account_tag_sync',NULL,NULL,3010,NULL,'1',0,'0','0','1','admin','2023-03-03 09:49:55','admin','2023-03-11 16:29:56','0',1),
	 (3015,'素材管理',NULL,'/biz/mp/wx-material/index',NULL,3000,'iconfont icon-tongzhi3','1',5,'0','0','0','admin','2023-02-27 14:13:47','admin','2023-11-01 17:28:35','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (3017,'素材删除','mp_wxmaterial_del',NULL,NULL,3015,NULL,'0',0,'0','0','1','admin','2023-02-27 14:14:18',' ',NULL,'0',1),
	 (3018,'自动回复',NULL,'/biz/mp/wx-auto-reply/index',NULL,3000,'iconfont icon-putong','1',4,'0','0','0','admin','2023-03-01 10:56:10','admin','2023-11-01 17:28:40','0',1),
	 (3019,'新增回复','mp_wxautoreply_add',NULL,NULL,3018,NULL,'0',0,'0','0','1','admin','2023-03-01 10:56:28',' ',NULL,'0',1),
	 (3020,'编辑回复','mp_wxautoreply_edit',NULL,NULL,3018,NULL,'0',0,'0','0','1','admin','2023-03-01 10:56:42',' ',NULL,'0',1),
	 (3021,'删除回复','mp_wxautoreply_del',NULL,NULL,3018,NULL,'0',0,'0','0','1','admin','2023-03-01 10:56:53',' ',NULL,'0',1),
	 (3022,'运营数据',NULL,'/biz/mp/wx-statistics/index',NULL,3000,'iconfont icon-shuxing','1',8,'0','0','0','admin','2023-03-01 11:15:58','admin','2023-11-01 17:28:54','0',1),
	 (3023,'新增消息','mp_wxmsg_add',NULL,NULL,3008,NULL,'0',0,'0','0','1','admin','2023-03-01 17:12:02',' ',NULL,'0',1),
	 (3024,'新增粉丝','mp_wxaccountfans_add','mp_wxaccountfans_add',NULL,3006,NULL,'0',0,'0','0','1','admin','2023-03-02 10:57:41',' ',NULL,'0',1),
	 (3025,'粉丝编辑','mp_wxaccountfans_edit','mp_wxaccountfans_add',NULL,3006,NULL,'0',0,'0','0','1','admin','2023-03-02 10:57:52',' ',NULL,'0',1),
	 (3026,'粉丝删除','mp_wxaccountfans_del','mp_wxaccountfans_add',NULL,3006,NULL,'0',0,'0','0','1','admin','2023-03-02 10:58:02',' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (3027,'新增菜单','mp_wxmenu_add',NULL,NULL,3002,NULL,'0',0,'0','0','1','admin','2023-02-27 20:54:34',' ',NULL,'0',1),
	 (3028,'发布菜单','mp_wxmenu_push',NULL,NULL,3002,NULL,'0',0,'0','0','1','admin','2023-02-27 20:54:48',' ',NULL,'0',1),
	 (3029,'删除菜单','mp_wxmenu_del',NULL,NULL,3002,NULL,'0',0,'0','0','1','admin','2023-02-27 20:54:57',' ',NULL,'0',1),
	 (4000,'系统监控',NULL,'/daemon',NULL,-1,'iconfont icon-shuju','1',3,'0','0','0','admin','2023-02-06 20:20:47','admin','2023-11-01 17:12:31','1',1),
	 (4001,'文档扩展',NULL,'http://pigx-gateway:9999/admin/doc.html',NULL,9910,'iconfont icon-biaodan','1',2,'0','1','0','','2018-06-26 10:50:32','admin','2023-11-27 14:52:54','0',1),
	 (4002,'缓存监控',NULL,'/tools/data/cache',NULL,9910,'iconfont icon-shuju','1',1,'0','0','0','admin','2023-05-29 15:12:59','admin','2023-11-27 14:52:56','0',1),
	 (4010,'信息推送','','/tools/message/index',NULL,9910,'iconfont icon-zhongduancanshuchaxun','1',7,'0',NULL,'0','',NULL,'admin','2023-11-27 14:52:57','0',1),
	 (4011,'信息推送查看','sys_message_view',NULL,NULL,4010,'1','1',0,'0',NULL,'1','',NULL,'admin','2023-10-25 14:51:54','0',1),
	 (4012,'信息推送新增','sys_message_add',NULL,NULL,4010,'1','1',1,'0',NULL,'1','',NULL,'admin','2023-10-25 14:52:00','0',1),
	 (4013,'信息推送修改','sys_message_edit',NULL,NULL,4010,'1','1',2,'0',NULL,'1','',NULL,'admin','2023-10-25 14:52:04','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (4014,'信息推送删除','sys_message_del',NULL,NULL,4010,'1','1',3,'0',NULL,'1','',NULL,'admin','2023-10-25 14:52:09','0',1),
	 (5000,'支付系统',NULL,'/pay',NULL,9900,'iconfont icon-neiqianshujuchucun','1',1,'0','0','0','admin','2023-02-27 10:57:14','admin','2023-11-27 14:52:11','0',1),
	 (5001,'收银台',NULL,'/biz/pay/cd/index',NULL,5000,'iconfont icon-diqiu1','1',0,'0','0','0','admin','2023-02-27 10:58:13','admin','2023-11-01 17:28:57','0',1),
	 (5002,'支付渠道',NULL,'/biz/pay/channel/index',NULL,5000,'iconfont icon-crew_feature','1',1,'0','0','0','admin','2023-02-27 19:36:55','admin','2023-11-01 17:29:01','0',1),
	 (5003,'查询','pay_channel_view',NULL,NULL,5002,NULL,'0',0,'0','0','1','admin','2023-02-27 19:41:44',' ',NULL,'0',1),
	 (5004,'新增','pay_channel_add',NULL,NULL,5002,NULL,'0',0,'0','0','1','admin','2023-02-27 19:42:05',' ',NULL,'0',1),
	 (5005,'编辑','pay_channel_edit',NULL,NULL,5002,NULL,'0',0,'0','0','1','admin','2023-02-27 19:42:23',' ',NULL,'0',1),
	 (5006,'删除','pay_channel_del',NULL,NULL,5002,NULL,'0',0,'0','0','1','admin','2023-02-27 19:42:40',' ',NULL,'0',1),
	 (5007,'导出','pay_channel_export',NULL,NULL,5002,NULL,'0',0,'0','0','1','admin','2023-02-27 19:42:57',' ',NULL,'0',1),
	 (5008,'商品订单',NULL,'/biz/pay/order/index',NULL,5000,'iconfont icon-fuwenbenkuang','1',2,'0','0','0','admin','2023-02-28 09:56:22',' ','2023-11-01 17:29:05','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (5009,'新增','pay_order_add',NULL,NULL,5008,NULL,'0',0,'0','0','1','admin','2023-02-28 09:58:25',' ',NULL,'0',1),
	 (5010,'删除','pay_order_del',NULL,NULL,5008,NULL,'0',0,'0','0','1','admin','2023-02-28 09:58:40',' ',NULL,'0',1),
	 (5011,'修改','pay_order_edit',NULL,NULL,5008,NULL,'0',0,'0','0','1','admin','2023-02-28 09:59:11',' ',NULL,'0',1),
	 (5012,'查找','pay_order_view',NULL,NULL,5008,NULL,'0',0,'0','0','1','admin','2023-02-28 09:59:37',' ',NULL,'0',1),
	 (5013,'导出','pay_order_export',NULL,NULL,5008,NULL,'0',0,'0','0','1','admin','2023-02-28 09:59:54',' ',NULL,'0',1),
	 (5014,'通知记录',NULL,'/biz/pay/record/index',NULL,5000,'iconfont icon-fuwenbenkuang','1',5,'0','0','0','admin','2023-02-28 11:01:37','admin','2023-11-01 17:29:08','0',1),
	 (5015,'新增','pay_record_add',NULL,NULL,5014,NULL,'1',0,'0','0','1','admin','2023-02-28 11:04:40',' ',NULL,'0',1),
	 (5016,'修改','pay_record_edit',NULL,NULL,5014,NULL,'1',0,'0','0','1','admin','2023-02-28 11:05:00',' ',NULL,'0',1),
	 (5017,'删除','pay_record_del',NULL,NULL,5014,NULL,'1',0,'0','0','1','admin','2023-02-28 11:05:15',' ',NULL,'0',1),
	 (5018,'导出','pay_record_export',NULL,NULL,5014,NULL,'1',0,'0','0','1','admin','2023-02-28 11:05:41',' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (5019,'查询','pay_record_view',NULL,NULL,5014,NULL,'0',0,'0','0','1','admin','2023-02-28 11:12:53',' ',NULL,'0',1),
	 (5020,'退款订单',NULL,'/biz/pay/refund/index',NULL,5000,'iconfont icon-fuwenbenkuang','1',4,'0','0','0','admin','2023-02-28 13:59:04','admin','2023-11-01 17:29:11','0',1),
	 (5021,'查询','pay_refund_view',NULL,NULL,5020,NULL,'0',0,'0','0','1','admin','2023-02-28 13:59:31',' ',NULL,'0',1),
	 (5022,'新增','pay_refund_add',NULL,NULL,5020,NULL,'0',0,'0','0','1','admin','2023-02-28 13:59:48',' ',NULL,'0',1),
	 (5023,'修改','pay_refund_edit',NULL,NULL,5020,NULL,'0',0,'0','0','1','admin','2023-02-28 14:00:05',' ',NULL,'0',1),
	 (5024,'删除','pay_refund_del',NULL,NULL,5020,NULL,'0',0,'0','0','1','admin','2023-02-28 14:00:23',' ',NULL,'0',1),
	 (5025,'导出','pay_refund_export',NULL,NULL,5020,NULL,'0',0,'0','0','1','admin','2023-02-28 14:00:35','admin','2023-02-28 14:04:15','0',1),
	 (5026,'支付订单',NULL,'/biz/pay/trade/index',NULL,5000,'iconfont icon-biaodan','1',3,'0','0','0','admin','2023-02-28 14:44:59','admin','2023-11-01 17:29:16','0',1),
	 (5027,'查询','pay_trade_view',NULL,NULL,5026,NULL,'0',0,'0','0','1','admin','2023-02-28 14:45:50',' ',NULL,'0',1),
	 (5028,'新增','pay_trade_add',NULL,NULL,5026,NULL,'0',0,'0','0','1','admin','2023-02-28 14:46:08',' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (5029,'修改','pay_trade_edit',NULL,NULL,5026,NULL,'0',0,'0','0','1','admin','2023-02-28 14:46:22',' ',NULL,'0',1),
	 (5030,'删除','pay_trade_del',NULL,NULL,5026,NULL,'0',0,'0','0','1','admin','2023-02-28 14:46:36',' ',NULL,'0',1),
	 (5031,'导出','pay_trade_export',NULL,NULL,5026,NULL,'0',0,'0','0','1','admin','2023-02-28 14:46:49',' ',NULL,'0',1),
	 (6000,'协同办公',NULL,'/flow',NULL,-1,'ele-Present','1',4,'0','0','0','admin','2023-03-02 16:36:49','admin','2023-11-01 17:09:28','0',1),
	 (6001,'流程管理',NULL,'/flow/group/index',NULL,6000,'iconfont icon-gongju','1',3,'0','0','0','admin','2023-03-02 16:37:55','admin','2023-11-01 17:10:20','0',1),
	 (6002,'创建流程',NULL,'/flow/create/all',NULL,6000,'fa fa-arrow-circle-right','0',2,'0',NULL,'0','','2023-07-27 13:14:56','admin','2023-07-27 13:32:32','0',1),
	 (6003,'发起流程',NULL,'/flow/list/index',NULL,6000,'fa fa-play','1',1,'0','0','0','admin','2023-03-02 18:18:10','admin','2023-07-27 13:29:00','0',1),
	 (6004,'任务管理',NULL,'/task',NULL,6000,'fa fa-th','1',0,'0','0','0','admin','2023-03-02 22:13:29','admin','2023-11-01 17:10:13','0',1),
	 (6005,'待办任务',NULL,'/flow/task/pending',NULL,6004,'fa fa-flag-checkered','1',0,'0','0','0','admin','2023-03-02 22:59:35','admin','2023-11-01 17:36:55','0',1),
	 (6006,'我的已办',NULL,'/flow/task/completed',NULL,6004,'fa fa-hand-o-right','1',3,'0','0','0','admin','2023-03-02 23:23:13','admin','2023-11-01 17:36:57','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (6007,'我的发起',NULL,'/flow/task/started',NULL,6004,'fa fa-plane','1',1,'0',NULL,'0','','2023-07-27 13:14:51','admin','2023-11-01 17:36:59','0',1),
	 (6008,'抄送给我',NULL,'/flow/task/cc',NULL,6004,'fa fa-arrow-circle-right','1',2,'0',NULL,'0','','2023-07-27 13:14:56','admin','2023-11-01 17:37:01','0',1),
	 (7000,'APP管理',NULL,'/app',NULL,9900,'ele-Cellphone','1',2,'0','0','0','admin',NULL,'admin','2023-11-27 14:52:31','0',1),
	 (7100,'客户管理',NULL,'/biz/app/appuser/index',NULL,7000,'ele-UserFilled','1',1,'1',NULL,'0','admin',NULL,'admin','2023-11-01 17:29:36','0',1),
	 (7101,'新增用户','app_appuser_add',NULL,NULL,7100,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:00','0',1),
	 (7102,'编辑用户','app_appuser_edit',NULL,NULL,7100,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:00','0',1),
	 (7103,'删除用户','app_appuser_del',NULL,NULL,7100,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:00','0',1),
	 (7104,'导出用户','app_appuser_export',NULL,NULL,7100,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:00','0',1),
	 (7200,'APP角色',NULL,'/biz/app/approle/index',NULL,7000,'ele-Stamp','1',2,'0','0','0','admin',NULL,'admin','2023-11-01 17:29:39','0',1),
	 (7201,'删除角色','app_approle_del',NULL,NULL,7200,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:01','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (7202,'编辑角色','app_approle_edit',NULL,NULL,7200,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:01','0',1),
	 (7203,'新增角色','app_approle_add',NULL,NULL,7200,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:01','0',1),
	 (7204,'导出角色','app_approle_export',NULL,NULL,7200,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:01','0',1),
	 (7300,'APP秘钥',NULL,'/biz/app/appsocial/index',NULL,7000,'iconfont icon-quanxian','1',3,'0','0','0','admin',NULL,'admin','2023-11-01 17:29:42','0',1),
	 (7301,'删除秘钥','app_social_details_del',NULL,NULL,7300,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:02','0',1),
	 (7302,'修改秘钥','app_social_details_edit',NULL,NULL,7300,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:02','0',1),
	 (7303,'保存秘钥','app_social_details_add',NULL,NULL,7300,NULL,'1',1,'0',NULL,'1','admin',NULL,'admin','2023-01-29 07:01:02','0',1),
	 (7400,'文章资讯','','/biz/app/appArticle/index',NULL,7000,'ele-CollectionTag','1',4,'0',NULL,'0','',NULL,'admin','2023-11-01 17:29:46','0',1),
	 (7401,'文章资讯表查看','app_appArticle_view',NULL,NULL,7400,'1','1',0,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7402,'文章资讯表新增','app_appArticle_add',NULL,NULL,7400,'1','1',1,'0',NULL,'1',' ',NULL,' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (7403,'文章资讯表修改','app_appArticle_edit',NULL,NULL,7400,'1','1',2,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7404,'文章资讯表删除','app_appArticle_del',NULL,NULL,7400,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7405,'导入导出','app_appArticle_export',NULL,NULL,7400,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7500,'文章分类','','/biz/app/appArticleCategory/index',NULL,7000,'iconfont icon-caidan','1',5,'0',NULL,'0','',NULL,'admin','2023-11-01 17:29:49','0',1),
	 (7501,'文章分类表查看','app_appArticleCategory_view',NULL,NULL,7500,'1','1',0,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7502,'文章分类表新增','app_appArticleCategory_add',NULL,NULL,7500,'1','1',1,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7503,'文章分类表修改','app_appArticleCategory_edit',NULL,NULL,7500,'1','1',2,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7504,'文章分类表删除','app_appArticleCategory_del',NULL,NULL,7500,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7505,'导入导出','app_appArticleCategory_export',NULL,NULL,7500,'1','1',3,'0',NULL,'1',' ',NULL,' ',NULL,'0',1),
	 (7600,'文章发布',NULL,'/biz/app/appArticle/form',NULL,7000,'iconfont icon-shuaxin','0',4,'0','0','0','admin','2023-06-07 17:05:32','admin','2023-11-01 17:29:52','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (7700,'界面设置','','/biz/app/page/index',NULL,7000,'iconfont icon-diannao1','1',8,'0',NULL,'0','',NULL,'admin','2023-11-01 17:29:55','0',1),
	 (7701,'底部导航',NULL,'/biz/app/tabbar/index',NULL,7000,'iconfont icon-neiqianshujuchucun','1',9,'0','0','0','admin','2023-06-14 14:36:08','admin','2023-11-01 17:29:59','0',1),
	 (9000,'开发平台',NULL,'/gen',NULL,-1,'iconfont icon-shuxingtu','1',9,'0','0','0','','2019-08-12 09:35:16','admin','2023-02-23 20:02:24','0',1),
	 (9005,'数据源管理',NULL,'/gen/datasource/index',NULL,9000,'ele-Coin','1',0,'0',NULL,'0','','2019-08-12 09:42:11','admin','2023-02-16 15:31:37','0',1),
	 (9006,'表单设计',NULL,'/gen/design/index',NULL,9000,'iconfont icon-AIshiyanshi','0',2,'0','0','0','','2019-08-16 10:08:56','admin','2023-02-23 14:06:50','0',1),
	 (9007,'生成页面',NULL,'/gen/gener/index',NULL,9000,'iconfont icon-tongzhi4','0',1,'0','0','0','admin','2023-02-20 09:58:23','admin','2023-02-20 14:41:43','0',1),
	 (9050,'元数据管理',NULL,'/gen/metadata',NULL,9000,'iconfont icon--chaifenhang','1',9,'0','0','0','','2018-07-27 01:13:21','admin','2023-02-23 19:55:10','0',1),
	 (9051,'模板管理',NULL,'/gen/template/index',NULL,9050,'iconfont icon--chaifenhang','1',5,'0','0','0','admin','2023-02-21 11:22:54','admin','2023-02-23 19:56:03','0',1),
	 (9052,'查询','codegen_template_view',NULL,NULL,9051,NULL,'0',0,'0','0','1','admin','2023-02-21 12:33:03','admin','2023-02-21 13:50:54','0',1),
	 (9053,'增加','codegen_template_add',NULL,NULL,9051,NULL,'1',0,'0','0','1','admin','2023-02-21 13:34:10','admin','2023-02-21 13:39:49','0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (9054,'新增','codegen_template_add',NULL,NULL,9051,NULL,'0',1,'0','0','1','admin','2023-02-21 13:51:32',' ',NULL,'0',1),
	 (9055,'导出','codegen_template_export',NULL,NULL,9051,NULL,'0',2,'0','0','1','admin','2023-02-21 13:51:58',' ',NULL,'0',1),
	 (9056,'删除','codegen_template_del',NULL,NULL,9051,NULL,'0',3,'0','0','1','admin','2023-02-21 13:52:16',' ',NULL,'0',1),
	 (9057,'编辑','codegen_template_edit',NULL,NULL,9051,NULL,'0',4,'0','0','1','admin','2023-02-21 13:52:58',' ',NULL,'0',1),
	 (9059,'模板分组',NULL,'/gen/group/index',NULL,9050,'iconfont icon-shuxingtu','1',6,'0','0','0','admin','2023-02-21 15:06:50','admin','2023-02-23 19:55:25','0',1),
	 (9060,'查询','codegen_group_view',NULL,NULL,9059,NULL,'0',0,'0','0','1','admin','2023-02-21 15:08:07',' ',NULL,'0',1),
	 (9061,'新增','codegen_group_add',NULL,NULL,9059,NULL,'0',0,'0','0','1','admin','2023-02-21 15:08:28',' ',NULL,'0',1),
	 (9062,'修改','codegen_group_edit',NULL,NULL,9059,NULL,'0',0,'0','0','1','admin','2023-02-21 15:08:43',' ',NULL,'0',1),
	 (9063,'删除','codegen_group_del',NULL,NULL,9059,NULL,'0',0,'0','0','1','admin','2023-02-21 15:09:02',' ',NULL,'0',1),
	 (9064,'导出','codegen_group_export',NULL,NULL,9059,NULL,'0',0,'0','0','1','admin','2023-02-21 15:09:22',' ',NULL,'0',1);
INSERT INTO public.sys_menu (menu_id,"name","permission","path",component,parent_id,icon,visible,sort_order,keep_alive,embedded,menu_type,create_by,create_time,update_by,update_time,del_flag,tenant_id) VALUES
	 (9065,'字段管理',NULL,'/gen/field-type/index',NULL,9050,'iconfont icon-fuwenben','1',0,'0','0','0','admin','2023-02-23 20:05:09','admin','2023-02-23 20:05:45','0',1),
	 (9070,'数据表管理',NULL,'/gen/create-table/index',NULL,9000,'iconfont icon-bolangneng','1',1,'0','0','0','admin','2024-02-19 11:41:12',' ',NULL,'0',1),
	 (9071,'新增','codegen_table_add',NULL,NULL,9070,NULL,'0',0,'0','0','1','admin','2023-02-21 15:08:28',' ',NULL,'0',1),
	 (9900,'业务平台',NULL,'/biz',NULL,-1,'iconfont icon-caidan','1',2,'0','0','0','admin','2023-11-01 17:07:23','admin','2023-11-27 14:51:31','0',1),
	 (9910,'基础工具',NULL,'/tools',NULL,-1,'iconfont icon-gongju','1',3,'0','0','0','admin','2023-11-01 17:12:02',' ','2023-11-27 14:53:13','0',1),
	 (9911,'路由管理',NULL,'/tools/route/index',NULL,9910,'iconfont icon-crew_feature','1',3,'0','0','0','admin','2023-11-01 17:13:09','admin','2023-11-27 14:53:33','0',1),
	 (9912,'大屏看板',NULL,'/tools/data/report',NULL,9910,'iconfont icon-shuju','1',5,'0','0','0','admin','2023-11-01 17:19:38','admin','2023-11-27 14:53:38','0',1),
	 (9913,'数据报表',NULL,'/tools/data/jimu',NULL,9910,'iconfont icon-ico_shuju','1',6,'0','0','0','admin','2023-11-01 17:20:06','admin','2023-11-27 14:53:43','0',1);
INSERT INTO public.sys_oauth_client_details (id,client_id,resource_ids,client_secret,"scope",authorized_grant_types,web_server_redirect_uri,authorities,access_token_validity,refresh_token_validity,additional_information,autoapprove,del_flag,create_by,update_by,create_time,update_time,tenant_id) VALUES
	 (1,'app',NULL,'app','server','password,refresh_token,authorization_code,client_credentials,mobile','http://localhost:4040/sso1/login,http://localhost:4041/sso1/login,http://localhost:8080/renren-admin/sys/oauth2-sso,http://localhost:8090/sys/oauth2-sso',NULL,43200,2592001,'{"enc_flag":"1","captcha_flag":"1","online_quantity":"1"}','true','0','','admin',NULL,'2023-02-09 13:54:54',1),
	 (2,'daemon',NULL,'daemon','server','password,refresh_token',NULL,NULL,43200,2592001,'{"enc_flag":"1","captcha_flag":"1"}','true','0',' ',' ',NULL,NULL,1),
	 (3,'gen',NULL,'gen','server','password,refresh_token',NULL,NULL,43200,2592001,'{"enc_flag":"1","captcha_flag":"1"}','true','0',' ',' ',NULL,NULL,1),
	 (4,'mp',NULL,'mp','server','password,refresh_token',NULL,NULL,43200,2592001,'{"enc_flag":"1","captcha_flag":"1"}','true','0',' ',' ',NULL,NULL,1),
	 (5,'pig',NULL,'pig','server','password,refresh_token,authorization_code,client_credentials,mobile','http://localhost:4040/sso1/login,http://localhost:4041/sso1/login,http://localhost:8080/renren-admin/sys/oauth2-sso,http://localhost:8090/sys/oauth2-sso',NULL,43200,2592001,'{"enc_flag":"1","captcha_flag":"1","online_quantity":"1"}','false','0','','admin',NULL,'2023-03-08 11:32:41',1),
	 (6,'test',NULL,'test','server','password,refresh_token',NULL,NULL,43200,2592001,'{ "enc_flag":"1","captcha_flag":"0"}','true','0',' ',' ',NULL,NULL,1),
	 (7,'social',NULL,'social','server','password,refresh_token,mobile',NULL,NULL,43200,2592001,'{ "enc_flag":"0","captcha_flag":"0"}','true','0',' ',' ',NULL,NULL,1),
	 (8,'mini',NULL,'mini','server','password,mobile',NULL,NULL,160000000,160000000,'{"captcha_flag":"0","enc_flag":"0","online_quantity":"1"}','true','0','admin','admin','2023-01-29 16:38:06','2023-01-29 17:21:56',1);
INSERT INTO public.sys_post (post_id,post_code,post_name,post_sort,remark,del_flag,create_time,create_by,update_time,update_by,tenant_id) VALUES
	 (1,'TEAM_LEADER','部门负责人',0,'LEADER','0','2022-03-26 13:48:17','','2023-03-08 16:03:35','admin',1);
INSERT INTO public.sys_public_param (public_id,public_name,public_key,public_value,status,validate_code,create_by,update_by,create_time,update_time,public_type,system_flag,del_flag,tenant_id) VALUES
	 (1,'租户默认来源','TENANT_DEFAULT_ID','1','0','',' ',' ','2020-05-12 04:03:46','2020-06-20 08:56:30','2','0','1',1),
	 (2,'租户默认部门名称','TENANT_DEFAULT_DEPTNAME','租户默认部门','0','',' ',' ','2020-05-12 03:36:32',NULL,'2','1','0',1),
	 (3,'租户默认账户','TENANT_DEFAULT_USERNAME','admin','0','',' ',' ','2020-05-12 04:05:04',NULL,'2','1','0',1),
	 (4,'租户默认密码','TENANT_DEFAULT_PASSWORD','123456','0','',' ',' ','2020-05-12 04:05:24',NULL,'2','1','0',1),
	 (5,'租户默认角色编码','TENANT_DEFAULT_ROLECODE','ROLE_ADMIN','0','',' ',' ','2020-05-12 04:05:57',NULL,'2','1','0',1),
	 (6,'租户默认角色名称','TENANT_DEFAULT_ROLENAME','租户默认角色','0','',' ',' ','2020-05-12 04:06:19',NULL,'2','1','0',1),
	 (7,'表前缀','GEN_TABLE_PREFIX','tb_','0','',' ',' ','2020-05-12 04:23:04',NULL,'9','1','0',1),
	 (8,'接口文档不显示的字段','GEN_HIDDEN_COLUMNS','tenant_id','0','',' ',' ','2020-05-12 04:25:19',NULL,'9','1','0',1),
	 (9,'注册用户默认角色','USER_DEFAULT_ROLE','GENERAL_USER','0',NULL,' ',' ','2022-03-31 16:52:24',NULL,'2','1','0',1);
INSERT INTO public.sys_role (role_id,role_name,role_code,role_desc,ds_type,ds_scope,create_by,update_by,create_time,update_time,del_flag,tenant_id) VALUES
	 (1,'管理员','ROLE_ADMIN','管理员','0','','','edg134','2017-10-29 15:45:51','2023-04-06 14:03:28','0',1),
	 (2,'普通用户','GENERAL_USER','普通用户','0','','','admin','2022-03-31 17:03:15','2023-04-03 02:28:51','0',1);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,1000),
	 (1,1100),
	 (1,1101),
	 (1,1102),
	 (1,1103),
	 (1,1104),
	 (1,1200),
	 (1,1201),
	 (1,1202),
	 (1,1203);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,1300),
	 (1,1301),
	 (1,1302),
	 (1,1303),
	 (1,1304),
	 (1,1305),
	 (1,1400),
	 (1,1401),
	 (1,1402),
	 (1,1403);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,1404),
	 (1,1500),
	 (1,1501),
	 (1,1502),
	 (1,1503),
	 (1,1504),
	 (1,1505),
	 (1,1506),
	 (1,1507),
	 (1,1508);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,1600),
	 (1,1601),
	 (1,1602),
	 (1,1603),
	 (1,1604),
	 (1,1605),
	 (1,2000),
	 (1,2001),
	 (1,2100),
	 (1,2101);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,2102),
	 (1,2103),
	 (1,2104),
	 (1,2105),
	 (1,2106),
	 (1,2200),
	 (1,2201),
	 (1,2202),
	 (1,2203),
	 (1,2210);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,2211),
	 (1,2212),
	 (1,2213),
	 (1,2300),
	 (1,2400),
	 (1,2401),
	 (1,2402),
	 (1,2403),
	 (1,2500),
	 (1,2501);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,2502),
	 (1,2503),
	 (1,2600),
	 (1,2601),
	 (1,2800),
	 (1,2810),
	 (1,2820),
	 (1,2830),
	 (1,2840),
	 (1,2850);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,2860),
	 (1,2870),
	 (1,2871),
	 (1,2900),
	 (1,2901),
	 (1,2902),
	 (1,2903),
	 (1,2904),
	 (1,2905),
	 (1,2906);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,2907),
	 (1,2910),
	 (1,2911),
	 (1,2912),
	 (1,2913),
	 (1,2914),
	 (1,2915),
	 (1,2920),
	 (1,2921),
	 (1,2922);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,2923),
	 (1,2924),
	 (1,2925),
	 (1,3000),
	 (1,3001),
	 (1,3002),
	 (1,3003),
	 (1,3004),
	 (1,3005),
	 (1,3006);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,3007),
	 (1,3008),
	 (1,3009),
	 (1,3010),
	 (1,3011),
	 (1,3012),
	 (1,3013),
	 (1,3014),
	 (1,3015),
	 (1,3016);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,3017),
	 (1,3018),
	 (1,3019),
	 (1,3020),
	 (1,3021),
	 (1,3022),
	 (1,3023),
	 (1,3024),
	 (1,3025),
	 (1,3026);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,3027),
	 (1,3028),
	 (1,3029),
	 (1,4000),
	 (1,4001),
	 (1,4002),
	 (1,4010),
	 (1,4011),
	 (1,4012),
	 (1,4013);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,4014),
	 (1,5000),
	 (1,5001),
	 (1,5002),
	 (1,5003),
	 (1,5004),
	 (1,5005),
	 (1,5006),
	 (1,5007),
	 (1,5008);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,5009),
	 (1,5010),
	 (1,5011),
	 (1,5012),
	 (1,5013),
	 (1,5014),
	 (1,5015),
	 (1,5016),
	 (1,5017),
	 (1,5018);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,5019),
	 (1,5020),
	 (1,5021),
	 (1,5022),
	 (1,5023),
	 (1,5024),
	 (1,5025),
	 (1,5026),
	 (1,5027),
	 (1,5028);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,5029),
	 (1,5030),
	 (1,5031),
	 (1,6000),
	 (1,6001),
	 (1,6002),
	 (1,6003),
	 (1,6004),
	 (1,6005),
	 (1,6006);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,6007),
	 (1,6008),
	 (1,7000),
	 (1,7100),
	 (1,7101),
	 (1,7102),
	 (1,7103),
	 (1,7104),
	 (1,7200),
	 (1,7201);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,7202),
	 (1,7203),
	 (1,7204),
	 (1,7300),
	 (1,7301),
	 (1,7302),
	 (1,7303),
	 (1,7400),
	 (1,7401),
	 (1,7402);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,7403),
	 (1,7404),
	 (1,7405),
	 (1,7500),
	 (1,7501),
	 (1,7502),
	 (1,7503),
	 (1,7504),
	 (1,7505),
	 (1,7600);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,7700),
	 (1,7701),
	 (1,9000),
	 (1,9005),
	 (1,9006),
	 (1,9007),
	 (1,9050),
	 (1,9051),
	 (1,9052),
	 (1,9053);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,9054),
	 (1,9055),
	 (1,9056),
	 (1,9057),
	 (1,9059),
	 (1,9060),
	 (1,9061),
	 (1,9062),
	 (1,9063),
	 (1,9064);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,9065),
	 (1,9070),
	 (1,9071),
	 (1,9700),
	 (1,9710),
	 (1,9712),
	 (1,9713),
	 (1,9714),
	 (1,9715),
	 (1,9720);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,9721),
	 (1,9722),
	 (1,9723),
	 (1,9724),
	 (1,9725),
	 (1,9730),
	 (1,9731),
	 (1,9732),
	 (1,9733),
	 (1,9734);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,9735),
	 (1,9740),
	 (1,9741),
	 (1,9742),
	 (1,9743),
	 (1,9744),
	 (1,9745),
	 (1,9750),
	 (1,9751),
	 (1,9752);
INSERT INTO public.sys_role_menu (role_id,menu_id) VALUES
	 (1,9753),
	 (1,9754),
	 (1,9755),
	 (1,9760),
	 (1,9900),
	 (1,9910),
	 (1,9911),
	 (1,9912),
	 (1,9913);
INSERT INTO public.sys_route_conf (id,route_name,route_id,predicates,filters,uri,sort_order,metadata,create_by,update_by,create_time,update_time,del_flag) VALUES
	 (1,'工作流管理模块','pigx-oa-platform','[{"args": {"_genkey_0": "/act/**"}, "name": "Path"}]','[]','lb://pigx-oa-platform',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:36:56','0'),
	 (2,'认证中心','pigx-auth','[{"args": {"_genkey_0": "/auth/**"}, "name": "Path"}]','[]','lb://pigx-auth',0,NULL,' ',' ','2019-10-16 16:44:41','2024-04-22 12:58:03','0'),
	 (3,'代码生成模块','pigx-codegen','[{"args": {"_genkey_0": "/gen/**"}, "name": "Path"}]','[]','lb://pigx-codegen',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:36:58','0'),
	 (4,'elastic-job定时任务模块','pigx-daemon-elastic-job','[{"args": {"_genkey_0": "/daemon/**"}, "name": "Path"}]','[]','lb://pigx-daemon-elastic-job',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:36:59','0'),
	 (5,'quartz定时任务模块','pigx-daemon-quartz','[{"args": {"_genkey_0": "/job/**"}, "name": "Path"}]','[]','lb://pigx-daemon-quartz',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:37:02','0'),
	 (6,'分布式事务模块','pigx-tx-manager','[{"args": {"_genkey_0": "/tx/**"}, "name": "Path"}]','[]','lb://pigx-tx-manager',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:37:04','0'),
	 (7,'通用权限模块','pigx-upms-biz','[{"args": {"_genkey_0": "/admin/**"}, "name": "Path"}]','[]','lb://pigx-upms-biz',0,'{"response-timeout": "30000"}',' ',' ','2019-10-16 16:44:41','2024-04-22 12:58:07','0'),
	 (8,'工作流长链接支持','pigx-oa-platform-ws','[{"args": {"_genkey_0": "/act/ws/**"}, "name": "Path"}]','[]','lb:ws://pigx-oa-platform',100,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:37:09','0'),
	 (9,'微信公众号管理','pigx-mp-platform','[{"args": {"_genkey_0": "/mp/**"}, "name": "Path"}]','[]','lb://pigx-mp-platform',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:37:12','0'),
	 (10,'支付管理','pigx-pay-platform','[{"args": {"_genkey_0": "/pay/**"}, "name": "Path"}]','[]','lb://pigx-pay-platform',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:37:13','0');
INSERT INTO public.sys_route_conf (id,route_name,route_id,predicates,filters,uri,sort_order,metadata,create_by,update_by,create_time,update_time,del_flag) VALUES
	 (11,'监控管理','pigx-monitor','[{"args": {"_genkey_0": "/monitor/**"}, "name": "Path"}]','[]','lb://pigx-monitor',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:37:17','0'),
	 (12,'积木报表','pigx-jimu-platform','[{"args": {"_genkey_0": "/jimu/**"}, "name": "Path"}]','[]','lb://pigx-jimu-platform',0,NULL,' ',' ','2019-10-16 16:44:41','2019-11-05 22:37:17','0'),
	 (13,'大屏设计','pigx-report-platform','[{"args": {"_genkey_0": "/gv/**"}, "name": "Path"}]','[]','lb://pigx-report-platform',0,'{}',' ',' ','2022-08-27 02:38:43','2023-04-05 07:52:27','0'),
	 (14,'APP服务','pigx-app-server','[{"args": {"_genkey_0": "/app/**"}, "name": "Path"}]','[]','lb://pigx-app-server-biz',0,'{}','admin',' ','2022-12-07 10:53:44',NULL,'0'),
	 (15,'工作流引擎','pigx-flow-task-biz','[{"args": {"_genkey_0": "/task/**"}, "name": "Path"}]','[]','lb://pigx-flow-task-biz',0,'{}',' ',' ','2023-07-28 16:50:26',NULL,'0'),
	 (16,'知识库','pigx-knowledge','[{"args": {"_genkey_0": "/knowledge/**"}, "name": "Path"}]','[]','lb://pigx-knowledge',0,'{}',' ',' ','2023-07-28 16:50:26',NULL,'0');
INSERT INTO public.sys_tenant (id,"name",code,tenant_domain,website_name,mini_qr,background,footer,logo,start_time,end_time,status,del_flag,create_by,update_by,create_time,update_time,menu_id) VALUES
	 (1,'北京分公司','1','',NULL,NULL,NULL,NULL,NULL,'2019-05-15 00:00:00','2029-05-15 00:00:00','0','0','','admin','2019-05-15 15:44:57','2023-07-30 14:52:57','1642752536722997250');
INSERT INTO public.sys_user (user_id,username,"password",salt,phone,avatar,nickname,"name",email,dept_id,create_by,update_by,create_time,update_time,lock_flag,password_expire_flag,password_modify_time,del_flag,wx_openid,mini_openid,qq_openid,gitee_login,osc_id,wx_cp_userid,wx_ding_userid,tenant_id) VALUES
	 (1,'admin','$2a$10$c/Ae0pRjJtMZg3BnvVpO.eIK6WYWVbKTzqgdy3afR7w.vd.xi3Mgy','','13054729089','/admin/sys-file/local/2a14ae08150e483c93e12ac8934173e2.png','管理员666777','管理员','<EMAIL>',4,' ','admin','2018-04-20 07:15:18','2023-04-03 14:00:06','0','0',NULL,'0',NULL,'oBxPy5E-v82xWGsfzZVzkD3wEX64',NULL,'log4j',NULL,NULL,NULL,1);
INSERT INTO public.sys_user_post (user_id,post_id) VALUES
	 (1,1);
INSERT INTO public.sys_user_role (user_id,role_id) VALUES
	 (1,1);
