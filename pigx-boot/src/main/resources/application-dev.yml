spring:
  data:
    redis:
      host: 127.0.0.1  # Redis地址
      port: 16379
      database: 1
      password: das321
  # 数据库相关配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      username: postgres
      password: agjfrtgfgf42gfdgfd
      url: jdbc:postgresql://*************:5432/huanggang3
#      driver-class-name: com.mysql.cj.jdbc.Driver
#      username: root
#      password: root
#      url: *************************************************************************************************************************************************************************************************************************************************************************************


# 文件上传配置
file:
  local:
    enable: true
    base-path: /usr/huanggang/img

# Logger Config
logging:
  level:
    org.springframework: INFO
    com.pig4cloud.pigx.admin.mapper: debug

# 验证码配置
aj:
  captcha:
    cache-type: redis
    water-mark: 黄冈市双控平台

# 登录报文加密根密钥 ，必须是16位
security:
  encodeKey: thanks,daspatial
  # 跳过验证码校验的客户端
  ignore-clients:
    - test

# 配置文件加密根密码
jasypt:
  encryptor:
    password: pigx

# swagger token url 配置
swagger:
  token-url: ${swagger.gateway}/admin/oauth2/token

