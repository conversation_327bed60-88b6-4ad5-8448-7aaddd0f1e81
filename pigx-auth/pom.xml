<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pig4cloud</groupId>
        <artifactId>pigx</artifactId>
        <version>5.7.0</version>
    </parent>

    <artifactId>pigx-auth</artifactId>
    <packaging>jar</packaging>

    <description>pigx 认证授权中心，基于 spring security oAuth2</description>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--log-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pigx-common-log</artifactId>
        </dependency>
        <!--security-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pigx-common-security</artifactId>
        </dependency>
        <!--feign 依赖-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pigx-common-feign</artifactId>
        </dependency>
        <!--缓存操作-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pigx-common-data</artifactId>
        </dependency>
        <!--sentinel 依赖-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pigx-common-sentinel</artifactId>
        </dependency>
        <!--路由控制-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pigx-common-gray</artifactId>
        </dependency>
        <!--freemarker-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <!-- 加签 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
        <!-- 调用验证码核心模块 -->
        <dependency>
            <groupId>io.springboot.plugin</groupId>
            <artifactId>captcha-core</artifactId>
            <version>${captcha.image.version}</version>
        </dependency>
        <!--滑块验证码-->
        <dependency>
            <groupId>io.springboot.plugin</groupId>
            <artifactId>spring-boot-starter-captcha</artifactId>
            <version>${captcha.version}</version>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <loaderImplementation>CLASSIC</loaderImplementation>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>boot</id>
        </profile>
    </profiles>

</project>
